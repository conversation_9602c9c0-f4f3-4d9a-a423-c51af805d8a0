version: '3.9'

services:
  postgres:
    image: postgres:16-alpine
    container_name: gg_postgres
    ports:
      - "5433:5432"
    environment:
      POSTGRES_USER: gg
      POSTGRES_PASSWORD: ggpassword
      POSTGRES_DB: gg
    volumes:
      - pg_data:/var/lib/postgresql/data
      - ../db/sql:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB || exit 1']
      interval: 5s
      timeout: 3s
      retries: 10

  nats:
    image: nats:2-alpine
    container_name: gg_nats
    command: ['-js', '-m', '8222']
    healthcheck:
      test: ['CMD-SHELL', 'wget -qO- http://localhost:8222/varz >/dev/null 2>&1 || exit 1']
      interval: 5s
      timeout: 3s
      retries: 10

volumes:
  pg_data:
