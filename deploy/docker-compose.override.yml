version: '3.9'

services:
  api-gateway:
    image: node:20-alpine
    working_dir: /app
    volumes:
      - ../services/api-gateway:/app
    environment:
      - ORDERS_SVC_URL=http://orders-svc:8081
      - FLEETS_SVC_URL=http://fleets-svc:8082
      - COLONIES_SVC_URL=http://colonies-svc:8083
      - EVENTS_WS_URL=ws://event-dispatcher:8090
    depends_on:
      orders-svc:
        condition: service_healthy
      fleets-svc:
        condition: service_healthy
      colonies-svc:
        condition: service_healthy
      event-dispatcher:
        condition: service_healthy
    healthcheck:
      test: ['CMD-SHELL', 'wget -qO- http://localhost:8080/healthz || exit 1']
      interval: 5s
      timeout: 3s
      retries: 6
    command: sh -lc "npm ci || npm i && npm run dev"

  orders-svc:
    image: node:20-alpine
    working_dir: /app
    volumes:
      - ../services/orders-svc:/app
    environment:
      - NATS_URL=nats://nats:4222
      # - USE_WASM_SIM=1
      # - SIM_WASM_PATH=/app/path/to/pkg.js
    depends_on:
      postgres:
        condition: service_healthy
      nats:
        condition: service_healthy
    healthcheck:
      test: ['CMD-SHELL', 'wget -qO- http://localhost:8081/healthz || exit 1']
      interval: 5s
      timeout: 3s
      retries: 6
    command: sh -lc "npm ci || npm i && npm run dev"

  event-dispatcher:
    image: node:20-alpine
    working_dir: /app
    volumes:
      - ../services/event-dispatcher:/app
    environment:
      - NATS_URL=nats://nats:4222
      - WS_PORT=8090
    depends_on:
      nats:
        condition: service_healthy
    healthcheck:
      test: ['CMD-SHELL', 'wget -qO- http://localhost:8090/healthz || exit 1']
      interval: 5s
      timeout: 3s
      retries: 6
    command: sh -lc "npm ci || npm i && npm run dev"

  fleets-svc:
    image: node:20-alpine
    working_dir: /app
    volumes:
      - ../services/fleets-svc:/app
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ['CMD-SHELL', 'wget -qO- http://localhost:8082/healthz || exit 1']
      interval: 5s
      timeout: 3s
      retries: 6
    command: sh -lc "npm ci || npm i && npm run dev"

  colonies-svc:
    image: node:20-alpine
    working_dir: /app
    volumes:
      - ../services/colonies-svc:/app
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ['CMD-SHELL', 'wget -qO- http://localhost:3003/health || exit 1']
      interval: 5s
      timeout: 3s
      retries: 6
    command: sh -lc "npm ci || npm i && npm run dev"

  nats:
    image: nats:2-alpine
    ports:
      - '4222:4222'
      - '8222:8222'
