import React, { useState } from 'react';
import StunningSolarSystemView from './StunningSolarSystemView';
import SolarSystemView from './SolarSystemView';

interface SolarSystemDemoProps {
  onBack: () => void;
}

const SolarSystemDemo: React.FC<SolarSystemDemoProps> = ({ onBack }) => {
  const [selectedStarId, setSelectedStarId] = useState<number>(1); // Default to Sol
  const [viewMode, setViewMode] = useState<'original' | 'stunning'>('stunning');

  // Sample star systems for demo
  const sampleStars = [
    { id: 1, name: 'Sol (Sun)', description: 'Our home star system' },
    { id: 2, name: 'Alpha Centauri A', description: 'Nearest star system' },
    { id: 3, name: 'Proxima Centauri', description: 'Red dwarf with exoplanets' },
    { id: 4, name: 'Wolf 359', description: 'Nearby red dwarf' },
    { id: 5, name: '<PERSON>\'s Star', description: 'High proper motion star' },
  ];

  if (selectedStarId) {
    if (viewMode === 'stunning') {
      return (
        <StunningSolarSystemView
          starId={selectedStarId}
          onBack={() => setSelectedStarId(0)}
        />
      );
    } else {
      return (
        <SolarSystemView
          starId={selectedStarId}
          onBack={() => setSelectedStarId(0)}
        />
      );
    }
  }

  return (
    <div className="w-full h-full bg-gradient-to-br from-indigo-900 via-purple-900 to-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 text-cyan-400">
            🌌 Solar System Viewer
          </h1>
          <p className="text-lg text-gray-300">
            Experience dramatic 3D solar system visualization with realistic orbital mechanics
          </p>
        </div>

        {/* Back button */}
        <button
          onClick={onBack}
          className="mb-6 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          ← Back to Galaxy
        </button>

        {/* View mode selector */}
        <div className="mb-8 text-center">
          <div className="inline-flex bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('stunning')}
              className={`px-6 py-2 rounded-md transition-colors ${
                viewMode === 'stunning'
                  ? 'bg-cyan-600 text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              ✨ Stunning View
            </button>
            <button
              onClick={() => setViewMode('original')}
              className={`px-6 py-2 rounded-md transition-colors ${
                viewMode === 'original'
                  ? 'bg-cyan-600 text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              📊 Original View
            </button>
          </div>
        </div>

        {/* Features comparison */}
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          <div className="bg-black bg-opacity-50 p-6 rounded-lg border border-cyan-400">
            <h3 className="text-xl font-bold mb-4 text-cyan-400">✨ Stunning View Features</h3>
            <ul className="space-y-2 text-sm">
              <li>🌟 <strong>Volumetric star coronas</strong> with animated plasma effects</li>
              <li>🪐 <strong>Realistic orbital mechanics</strong> based on Kepler's laws</li>
              <li>🌍 <strong>Atmospheric scattering</strong> for planets</li>
              <li>💫 <strong>Particle systems</strong> for stellar wind and effects</li>
              <li>🎨 <strong>Post-processing</strong> with bloom, depth of field, and tone mapping</li>
              <li>⏰ <strong>Time control</strong> with adjustable simulation speed</li>
              <li>🛸 <strong>Orbital trails</strong> showing planet paths</li>
              <li>💍 <strong>Ring systems</strong> for gas giants</li>
            </ul>
          </div>

          <div className="bg-black bg-opacity-50 p-6 rounded-lg border border-gray-400">
            <h3 className="text-xl font-bold mb-4 text-gray-400">📊 Original View Features</h3>
            <ul className="space-y-2 text-sm">
              <li>⭐ Basic star rendering</li>
              <li>🪐 Static planet positions</li>
              <li>📍 Simple orbital rings</li>
              <li>💡 Standard lighting</li>
              <li>📊 Basic planet information</li>
              <li>🎯 Click interactions</li>
            </ul>
          </div>
        </div>

        {/* Star system selection */}
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-6 text-cyan-400">Select a Star System</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
            {sampleStars.map((star) => (
              <button
                key={star.id}
                onClick={() => setSelectedStarId(star.id)}
                className="bg-black bg-opacity-50 p-4 rounded-lg border border-gray-600 hover:border-cyan-400 transition-colors text-left group"
              >
                <div className="text-lg font-bold text-cyan-400 group-hover:text-cyan-300 mb-2">
                  {star.name}
                </div>
                <div className="text-sm text-gray-400 group-hover:text-gray-300">
                  {star.description}
                </div>
                <div className="mt-3 text-xs text-cyan-500 group-hover:text-cyan-400">
                  Click to explore →
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Technical details */}
        <div className="mt-12 text-center">
          <h3 className="text-xl font-bold mb-4 text-cyan-400">🔬 Technical Improvements</h3>
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-black bg-opacity-30 p-4 rounded-lg">
              <div className="text-lg font-bold mb-2">🎯 Orbital Mechanics</div>
              <div className="text-sm text-gray-300">
                Real Keplerian orbital calculations with elliptical paths, 
                proper periods, and inclinations
              </div>
            </div>
            <div className="bg-black bg-opacity-30 p-4 rounded-lg">
              <div className="text-lg font-bold mb-2">🎨 Advanced Shaders</div>
              <div className="text-sm text-gray-300">
                Custom atmospheric scattering, volumetric coronas, 
                and distortion materials
              </div>
            </div>
            <div className="bg-black bg-opacity-30 p-4 rounded-lg">
              <div className="text-lg font-bold mb-2">⚡ Performance</div>
              <div className="text-sm text-gray-300">
                Optimized rendering with LOD, instancing, 
                and efficient particle systems
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SolarSystemDemo;
