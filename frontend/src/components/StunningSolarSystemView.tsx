import React, { useRef, useState, useEffect, useMemo, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import {
  OrbitControls,
  Stars,
  Float,
  Html,
  Ring,
  MeshDistortMaterial,
  Sparkles
} from '@react-three/drei';
import {
  EffectComposer,
  Bloom,
  ChromaticAberration,
  ToneMapping
} from '@react-three/postprocessing';
import * as THREE from 'three';

// Realistic solar system data for proper scaling
const SOLAR_SYSTEM_DATA = {
  mercury: { distance: 0.39, radius: 0.383, color: '#8C7853', period: 88 },
  venus: { distance: 0.72, radius: 0.949, color: '#FFC649', period: 225 },
  earth: { distance: 1.0, radius: 1.0, color: '#6B93D6', period: 365 },
  mars: { distance: 1.52, radius: 0.532, color: '#CD5C5C', period: 687 },
  jupiter: { distance: 5.2, radius: 11.21, color: '#D8CA9D', period: 4333 },
  saturn: { distance: 9.5, radius: 9.45, color: '#FAD5A5', period: 10759 },
  uranus: { distance: 19.2, radius: 4.01, color: '#4FD0E7', period: 30687 },
  neptune: { distance: 30.1, radius: 3.88, color: '#4B70DD', period: 60190 }
};

// Scale factors for better visualization
const DISTANCE_SCALE = 8; // Scale orbital distances
const SIZE_SCALE = 0.3; // Scale planet sizes
const SUN_SIZE = 2.5; // Sun size

// Beautiful orbital ring component
interface OrbitalRingProps {
  radius: number;
  color?: string;
  opacity?: number;
  segments?: number;
}

const OrbitalRing: React.FC<OrbitalRingProps> = ({ 
  radius, 
  color = '#444444', 
  opacity = 0.3,
  segments = 128 
}) => {
  const ringRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (ringRef.current) {
      // Subtle pulsing effect
      const pulse = 1 + Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      ringRef.current.material.opacity = opacity * pulse;
    }
  });

  return (
    <mesh ref={ringRef} rotation={[Math.PI / 2, 0, 0]}>
      <ringGeometry args={[radius - 0.05, radius + 0.05, segments]} />
      <meshBasicMaterial
        color={color}
        transparent
        opacity={opacity}
        side={THREE.DoubleSide}
      />
    </mesh>
  );
};

interface EnhancedPlanetProps {
  planetData: any;
  orbitalRadius: number;
  time: number;
  onClick: () => void;
  isSelected?: boolean;
}

const EnhancedPlanet: React.FC<EnhancedPlanetProps> = ({ 
  planetData, 
  orbitalRadius, 
  time, 
  onClick,
  isSelected = false
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const atmosphereRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // Calculate current orbital position
  const angle = (time * 2 * Math.PI) / planetData.period;
  const position: [number, number, number] = [
    Math.cos(angle) * orbitalRadius,
    0,
    Math.sin(angle) * orbitalRadius
  ];

  // Planet size with proper scaling
  const planetSize = planetData.radius * SIZE_SCALE;

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.005; // Planet rotation
    }
    if (atmosphereRef.current) {
      // Subtle atmosphere animation
      atmosphereRef.current.rotation.y -= 0.002;
    }
  });

  return (
    <group position={position}>
      {/* Main planet with enhanced materials */}
      <Float speed={0.2} rotationIntensity={0.02} floatIntensity={0.01}>
        <mesh
          ref={meshRef}
          onClick={onClick}
          onPointerEnter={() => setHovered(true)}
          onPointerLeave={() => setHovered(false)}
          scale={isSelected ? 1.2 : 1}
        >
          <sphereGeometry args={[planetSize, 64, 64]} />
          <MeshDistortMaterial
            color={planetData.color}
            emissive={planetData.color}
            emissiveIntensity={hovered ? 0.3 : 0.1}
            distort={0.05}
            speed={1}
            roughness={0.7}
            metalness={0.1}
          />
        </mesh>
      </Float>

      {/* Atmospheric glow */}
      <mesh ref={atmosphereRef} scale={planetSize * 1.3}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial 
          color={planetData.color}
          opacity={hovered ? 0.4 : 0.2}
          transparent
          side={THREE.BackSide}
        />
      </mesh>

      {/* Saturn's rings */}
      {planetData === SOLAR_SYSTEM_DATA.saturn && (
        <group rotation={[Math.PI / 2, 0, 0]}>
          <Ring args={[planetSize * 1.2, planetSize * 2.2, 64]}>
            <meshBasicMaterial
              color="#D4AF37"
              transparent
              opacity={0.7}
              side={THREE.DoubleSide}
            />
          </Ring>
          <Ring args={[planetSize * 2.3, planetSize * 2.8, 64]}>
            <meshBasicMaterial
              color="#CD853F"
              transparent
              opacity={0.5}
              side={THREE.DoubleSide}
            />
          </Ring>
        </group>
      )}

      {/* Planet label on hover */}
      {hovered && (
        <Html position={[0, planetSize + 1.5, 0]} center>
          <div className="bg-black bg-opacity-90 text-white px-4 py-2 rounded-lg border border-cyan-400 shadow-lg">
            <div className="font-bold text-lg text-cyan-400 mb-1">
              {Object.keys(SOLAR_SYSTEM_DATA).find(key => 
                SOLAR_SYSTEM_DATA[key as keyof typeof SOLAR_SYSTEM_DATA] === planetData
              )?.toUpperCase()}
            </div>
            <div className="text-sm space-y-1">
              <div>Distance: {planetData.distance} AU</div>
              <div>Radius: {planetData.radius.toFixed(2)} R⊕</div>
              <div>Period: {planetData.period} days</div>
            </div>
          </div>
        </Html>
      )}
    </group>
  );
};

// Beautiful Sun component
const StunningSun: React.FC = () => {
  const meshRef = useRef<THREE.Mesh>(null);
  const coronaRef = useRef<THREE.Mesh>(null);
  const flareRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.002;
    }
    if (coronaRef.current) {
      coronaRef.current.rotation.y -= 0.001;
      coronaRef.current.rotation.z += 0.0005;
    }
    if (flareRef.current) {
      const time = state.clock.elapsedTime;
      flareRef.current.scale.setScalar(1 + Math.sin(time * 1.5) * 0.1);
    }
  });

  return (
    <group position={[0, 0, 0]}>
      {/* Main Sun */}
      <Float speed={0.1} rotationIntensity={0.01} floatIntensity={0.005}>
        <mesh ref={meshRef}>
          <sphereGeometry args={[SUN_SIZE, 128, 128]} />
          <MeshDistortMaterial
            color="#FDB813"
            emissive="#FF6B00"
            emissiveIntensity={1.2}
            distort={0.15}
            speed={3}
            roughness={0}
            metalness={0}
          />
        </mesh>
      </Float>

      {/* Corona layers */}
      <mesh ref={coronaRef} scale={SUN_SIZE * 1.5}>
        <sphereGeometry args={[1, 64, 64]} />
        <meshBasicMaterial
          color="#FFD700"
          transparent
          opacity={0.2}
          side={THREE.BackSide}
        />
      </mesh>

      <mesh ref={flareRef} scale={SUN_SIZE * 2.2}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#FF8C00"
          transparent
          opacity={0.1}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Solar wind particles */}
      <Sparkles
        count={300}
        scale={[SUN_SIZE * 6, SUN_SIZE * 6, SUN_SIZE * 6]}
        size={3}
        speed={0.8}
        opacity={0.7}
        color="#FFD700"
      />

      {/* Sun label */}
      <Html position={[0, SUN_SIZE + 3, 0]} center>
        <div className="bg-black bg-opacity-90 text-white px-4 py-3 rounded-lg border border-yellow-400 shadow-lg">
          <div className="font-bold text-2xl text-yellow-400 mb-1">☉ The Sun</div>
          <div className="text-sm space-y-1">
            <div>Type: G-class Star</div>
            <div>Temperature: 5,778 K</div>
            <div>Age: 4.6 billion years</div>
          </div>
        </div>
      </Html>
    </group>
  );
};

// Time control component
interface TimeControlProps {
  timeSpeed: number;
  onTimeSpeedChange: (speed: number) => void;
  isPaused: boolean;
  onPauseToggle: () => void;
}

const TimeControl: React.FC<TimeControlProps> = ({
  timeSpeed,
  onTimeSpeedChange,
  isPaused,
  onPauseToggle
}) => {
  const speeds = [0.1, 0.5, 1, 2, 5, 10, 50, 100];

  return (
    <div className="absolute bottom-4 left-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-blue-400">
      <div className="text-sm font-bold mb-2 text-blue-400">Time Control</div>
      <div className="flex items-center space-x-2 mb-2">
        <button
          onClick={onPauseToggle}
          className={`px-3 py-1 rounded text-xs ${
            isPaused ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'
          }`}
        >
          {isPaused ? '▶ Play' : '⏸ Pause'}
        </button>
      </div>
      <div className="text-xs mb-1">Speed: {timeSpeed}x</div>
      <div className="flex flex-wrap gap-1">
        {speeds.map(speed => (
          <button
            key={speed}
            onClick={() => onTimeSpeedChange(speed)}
            className={`px-2 py-1 rounded text-xs ${
              timeSpeed === speed
                ? 'bg-blue-600 text-white'
                : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
            }`}
          >
            {speed}x
          </button>
        ))}
      </div>
    </div>
  );
};

// Main stunning solar system component
interface StunningSolarSystemViewProps {
  onClose?: () => void;
}

const StunningSolarSystemView: React.FC<StunningSolarSystemViewProps> = ({
  onClose
}) => {
  const [selectedPlanet, setSelectedPlanet] = useState<string | null>(null);
  const [timeSpeed, setTimeSpeed] = useState(1);
  const [isPaused, setIsPaused] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [showOrbitalTrails, setShowOrbitalTrails] = useState(true);
  const [enablePostProcessing, setEnablePostProcessing] = useState(true);

  // Time progression using useEffect instead of useFrame (which is only for Canvas children)
  useEffect(() => {
    if (isPaused) return;

    const interval = setInterval(() => {
      setCurrentTime(prev => prev + timeSpeed * 0.5);
    }, 16); // ~60fps

    return () => clearInterval(interval);
  }, [isPaused, timeSpeed]);

  // Create planets array from our solar system data
  const planets = Object.entries(SOLAR_SYSTEM_DATA).map(([name, data]) => ({
    name,
    ...data,
    orbitalRadius: data.distance * DISTANCE_SCALE
  }));

  return (
    <div className="relative w-full h-full bg-black overflow-hidden">
      {/* 3D Scene */}
      <Canvas
        camera={{
          position: [0, 30, 50],
          fov: 75,
          near: 0.1,
          far: 2000
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        {/* Lighting setup */}
        <ambientLight intensity={0.05} />
        <pointLight position={[0, 0, 0]} intensity={3} color="#FDB813" decay={0.1} />

        {/* Camera controls with smooth movement */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={15}
          maxDistance={300}
          autoRotate={false}
          autoRotateSpeed={0.2}
          dampingFactor={0.05}
          enableDamping={true}
        />

        {/* Beautiful starfield background */}
        <Stars
          radius={500}
          depth={100}
          count={3000}
          factor={6}
          saturation={0}
          fade
          speed={0.3}
        />

        {/* Central Sun */}
        <StunningSun />

        {/* Orbital rings and planets */}
        {planets.map((planetData, index) => (
          <group key={planetData.name}>
            {/* Beautiful orbital ring */}
            {showOrbitalTrails && (
              <OrbitalRing
                radius={planetData.orbitalRadius}
                color="#666666"
                opacity={0.4}
                segments={256}
              />
            )}

            {/* Planet */}
            <EnhancedPlanet
              planetData={planetData}
              orbitalRadius={planetData.orbitalRadius}
              time={currentTime}
              onClick={() => setSelectedPlanet(planetData.name)}
              isSelected={selectedPlanet === planetData.name}
            />
          </group>
        ))}

        {/* Post-processing effects for stunning visuals */}
        {enablePostProcessing && (
          <EffectComposer>
            <Bloom
              intensity={0.8}
              luminanceThreshold={0.1}
              luminanceSmoothing={0.9}
              height={400}
            />
            <ChromaticAberration
              offset={[0.001, 0.001]}
            />
            <ToneMapping
              adaptive={true}
              resolution={256}
              middleGrey={0.6}
              maxLuminance={16}
              averageLuminance={1}
              adaptationRate={1}
            />
          </EffectComposer>
        )}
      </Canvas>

      {/* Time Control */}
      <TimeControl
        timeSpeed={timeSpeed}
        onTimeSpeedChange={setTimeSpeed}
        isPaused={isPaused}
        onPauseToggle={() => setIsPaused(!isPaused)}
      />

      {/* Visual Options */}
      <div className="absolute bottom-4 right-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-blue-400">
        <div className="text-sm font-bold mb-2 text-blue-400">Visual Options</div>
        <div className="space-y-2">
          <label className="flex items-center space-x-2 text-xs">
            <input
              type="checkbox"
              checked={showOrbitalTrails}
              onChange={(e) => setShowOrbitalTrails(e.target.checked)}
              className="rounded"
            />
            <span>Orbital Rings</span>
          </label>
          <label className="flex items-center space-x-2 text-xs">
            <input
              type="checkbox"
              checked={enablePostProcessing}
              onChange={(e) => setEnablePostProcessing(e.target.checked)}
              className="rounded"
            />
            <span>Post-Processing</span>
          </label>
        </div>
      </div>

      {/* System Info Panel */}
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-yellow-400 max-w-xs">
        <div className="text-lg font-bold mb-2 text-yellow-400">☉ Solar System</div>
        <div className="text-sm space-y-1">
          <div>🌟 Our Sun (G-class star)</div>
          <div>🌡️ 5,778 K surface temperature</div>
          <div>🪐 8 planets in orbit</div>
          <div>⏱️ Time: {(currentTime / 10).toFixed(1)} years</div>
          {selectedPlanet && (
            <div className="mt-2 pt-2 border-t border-gray-600">
              <div className="text-cyan-400 font-bold">Selected: {selectedPlanet.toUpperCase()}</div>
            </div>
          )}
        </div>
      </div>

      {/* Close button */}
      {onClose && (
        <button
          onClick={onClose}
          className="absolute top-4 left-4 z-10 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-white"
        >
          ✕ Close
        </button>
      )}
    </div>
  );
};

export default StunningSolarSystemView;
