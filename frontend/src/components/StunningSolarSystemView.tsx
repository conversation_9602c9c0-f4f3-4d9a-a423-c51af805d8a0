import React, { useRef, useEffect, useState, useMemo, Suspense } from 'react';
import { Canvas, useFrame, extend } from '@react-three/fiber';
import {
  OrbitControls,
  Float,
  Html,
  Sparkles,
  Stars,
  Ring,
  MeshDistortMaterial,
  shaderMaterial
} from '@react-three/drei';
import { 
  EffectComposer, 
  Bloom, 
  ChromaticAberration, 
  Vignette,
  DepthOfField,
  Noise,
  ToneMapping
} from '@react-three/postprocessing';
import * as THREE from 'three';
import { stellarApi } from '../services/stellarApi';
import type { StarDetail, Planet } from '../types/stellar';

interface StunningSolarSystemViewProps {
  starId: number;
  onBack: () => void;
}

// For now, we'll use standard materials instead of custom shaders to avoid compilation issues
// TODO: Implement custom atmospheric shaders later

// Enhanced orbital mechanics
interface OrbitalData {
  semiMajorAxis: number;
  eccentricity: number;
  inclination: number;
  longitudeOfAscendingNode: number;
  argumentOfPeriapsis: number;
  meanAnomalyAtEpoch: number;
  orbitalPeriod: number;
}

const calculateOrbitalPosition = (
  orbitalData: OrbitalData, 
  time: number
): [number, number, number] => {
  const { 
    semiMajorAxis, 
    eccentricity, 
    inclination, 
    longitudeOfAscendingNode, 
    argumentOfPeriapsis, 
    meanAnomalyAtEpoch,
    orbitalPeriod 
  } = orbitalData;

  // Calculate mean anomaly
  const meanAnomaly = meanAnomalyAtEpoch + (2 * Math.PI * time) / orbitalPeriod;
  
  // Solve Kepler's equation for eccentric anomaly (simplified)
  let eccentricAnomaly = meanAnomaly;
  for (let i = 0; i < 5; i++) {
    eccentricAnomaly = meanAnomaly + eccentricity * Math.sin(eccentricAnomaly);
  }
  
  // Calculate true anomaly
  const trueAnomaly = 2 * Math.atan2(
    Math.sqrt(1 + eccentricity) * Math.sin(eccentricAnomaly / 2),
    Math.sqrt(1 - eccentricity) * Math.cos(eccentricAnomaly / 2)
  );
  
  // Calculate distance from focus
  const radius = semiMajorAxis * (1 - eccentricity * Math.cos(eccentricAnomaly));
  
  // Position in orbital plane
  const x = radius * Math.cos(trueAnomaly);
  const y = radius * Math.sin(trueAnomaly);
  
  // Apply orbital rotations
  const cosLAN = Math.cos(longitudeOfAscendingNode);
  const sinLAN = Math.sin(longitudeOfAscendingNode);
  const cosAOP = Math.cos(argumentOfPeriapsis);
  const sinAOP = Math.sin(argumentOfPeriapsis);
  const cosInc = Math.cos(inclination);
  const sinInc = Math.sin(inclination);
  
  // Transform to 3D space
  const xFinal = (cosLAN * cosAOP - sinLAN * sinAOP * cosInc) * x + 
                 (-cosLAN * sinAOP - sinLAN * cosAOP * cosInc) * y;
  const yFinal = (sinLAN * cosAOP + cosLAN * sinAOP * cosInc) * x + 
                 (-sinLAN * sinAOP + cosLAN * cosAOP * cosInc) * y;
  const zFinal = (sinAOP * sinInc) * x + (cosAOP * sinInc) * y;
  
  return [xFinal, yFinal, zFinal];
};

interface EnhancedPlanetProps {
  planet: Planet;
  orbitalData: OrbitalData;
  time: number;
  onClick: () => void;
}

const EnhancedPlanet: React.FC<EnhancedPlanetProps> = ({
  planet,
  orbitalData,
  time,
  onClick
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const atmosphereRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // Calculate current orbital position
  const position = useMemo(() => 
    calculateOrbitalPosition(orbitalData, time), 
    [orbitalData, time]
  );

  // Enhanced planet properties
  const planetSize = Math.max(0.1, Math.min(3.0, (planet.radius_earth || 0.5) * 0.4));
  
  const getPlanetMaterial = (composition: string) => {
    switch (composition.toLowerCase()) {
      case 'rocky':
        return {
          color: '#8B4513',
          emissive: '#2D1810',
          roughness: 0.9,
          metalness: 0.1,
          atmosphereColor: '#87CEEB'
        };
      case 'gas_giant':
        return {
          color: '#FFA500',
          emissive: '#FF6B00',
          roughness: 0.3,
          metalness: 0.0,
          atmosphereColor: '#FFD700'
        };
      case 'ice_giant':
        return {
          color: '#4169E1',
          emissive: '#1E3A8A',
          roughness: 0.2,
          metalness: 0.1,
          atmosphereColor: '#87CEFA'
        };
      case 'super_earth':
        return {
          color: '#228B22',
          emissive: '#0F4F0F',
          roughness: 0.7,
          metalness: 0.2,
          atmosphereColor: '#98FB98'
        };
      default:
        return {
          color: '#696969',
          emissive: '#2F2F2F',
          roughness: 0.8,
          metalness: 0.1,
          atmosphereColor: '#D3D3D3'
        };
    }
  };

  const material = getPlanetMaterial(planet.composition);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
    }
    if (atmosphereRef.current && atmosphereRef.current.material) {
      (atmosphereRef.current.material as any).time = state.clock.elapsedTime;
    }
  });

  return (
    <group position={position}>
      {/* Main planet */}
      <Float speed={0.3} rotationIntensity={0.05} floatIntensity={0.02}>
        <mesh
          ref={meshRef}
          onClick={onClick}
          onPointerEnter={() => setHovered(true)}
          onPointerLeave={() => setHovered(false)}
        >
          <sphereGeometry args={[planetSize, 64, 64]} />
          <meshStandardMaterial
            color={material.color}
            emissive={material.emissive}
            emissiveIntensity={planet.in_habitable_zone ? 0.3 : 0.1}
            roughness={material.roughness}
            metalness={material.metalness}
          />
        </mesh>
      </Float>

      {/* Atmospheric layer */}
      <mesh ref={atmosphereRef} scale={planetSize * 1.2}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color={material.atmosphereColor}
          opacity={0.4}
          transparent
          side={THREE.BackSide}
        />
      </mesh>

      {/* Ring system for gas giants */}
      {(planet.composition === 'gas_giant' || planet.composition === 'ice_giant') && (
        <Ring args={[planetSize * 1.5, planetSize * 2.5, 64]} rotation={[Math.PI / 2, 0, 0]}>
          <meshBasicMaterial
            color="#CCCCCC"
            transparent
            opacity={0.6}
            side={THREE.DoubleSide}
          />
        </Ring>
      )}

      {/* Enhanced planet info */}
      {hovered && (
        <Html position={[0, planetSize + 1, 0]} center>
          <div className="bg-black bg-opacity-90 text-white px-3 py-2 rounded-lg border border-cyan-400">
            <div className="font-bold text-cyan-400">{planet.name}</div>
            <div className="text-xs space-y-1">
              <div>Mass: {planet.mass_earth?.toFixed(2) || '?'} M⊕</div>
              <div>Radius: {planet.radius_earth?.toFixed(2) || '?'} R⊕</div>
              <div>Distance: {planet.sma_au?.toFixed(3) || '?'} AU</div>
              <div>Type: {planet.composition}</div>
              <div>Period: {orbitalData.orbitalPeriod.toFixed(1)} days</div>
              {planet.in_habitable_zone && (
                <div className="text-green-400 font-bold">🌍 Habitable Zone</div>
              )}
            </div>
          </div>
        </Html>
      )}
    </group>
  );
};

// Enhanced star with volumetric corona and solar flares
interface EnhancedStarProps {
  star: StarDetail;
}

const EnhancedStar: React.FC<EnhancedStarProps> = ({ star }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const coronaRef = useRef<THREE.Mesh>(null);
  const flareRef = useRef<THREE.Mesh>(null);

  const starSize = Math.max(0.8, Math.min(5.0, (star.radius_solar || 1.0) * 1.2));

  const getStarProperties = (spectralType?: string) => {
    if (!spectralType) return { color: '#FFFFFF', temperature: 5778 };
    const type = spectralType.charAt(0).toUpperCase();
    switch (type) {
      case 'O': return { color: '#9BB0FF', temperature: 30000 };
      case 'B': return { color: '#AABFFF', temperature: 15000 };
      case 'A': return { color: '#CAD7FF', temperature: 8500 };
      case 'F': return { color: '#F8F7FF', temperature: 6500 };
      case 'G': return { color: '#FFF4EA', temperature: 5778 };
      case 'K': return { color: '#FFD2A1', temperature: 4500 };
      case 'M': return { color: '#FFAD51', temperature: 3000 };
      default: return { color: '#FFFFFF', temperature: 5778 };
    }
  };

  const starProps = getStarProperties(star.spectral_type);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.003;
    }
    if (coronaRef.current) {
      coronaRef.current.rotation.y -= 0.001;
      coronaRef.current.rotation.z += 0.002;
    }
    if (flareRef.current) {
      const time = state.clock.elapsedTime;
      flareRef.current.scale.setScalar(1 + Math.sin(time * 2) * 0.1);
    }
  });

  return (
    <group position={[0, 0, 0]}>
      {/* Main star body */}
      <Float speed={0.1} rotationIntensity={0.02} floatIntensity={0.01}>
        <mesh ref={meshRef}>
          <sphereGeometry args={[starSize, 128, 128]} />
          <MeshDistortMaterial
            color={starProps.color}
            emissive={starProps.color}
            emissiveIntensity={0.8}
            distort={0.1}
            speed={2}
            roughness={0}
            metalness={0}
          />
        </mesh>
      </Float>

      {/* Volumetric corona */}
      <mesh ref={coronaRef} scale={starSize * 1.8}>
        <sphereGeometry args={[1, 64, 64]} />
        <meshBasicMaterial
          color={starProps.color}
          transparent
          opacity={0.15}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Solar flares */}
      <mesh ref={flareRef} scale={starSize * 2.5}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color={starProps.color}
          transparent
          opacity={0.05}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Stellar wind particles */}
      <Sparkles
        count={200}
        scale={[starSize * 4, starSize * 4, starSize * 4]}
        size={2}
        speed={0.5}
        opacity={0.6}
        color={starProps.color}
      />

      {/* Star information */}
      <Html position={[0, starSize + 2, 0]} center>
        <div className="bg-black bg-opacity-90 text-white px-4 py-3 rounded-lg border border-yellow-400">
          <div className="font-bold text-xl text-yellow-400">{star.name}</div>
          <div className="text-sm space-y-1">
            <div>Class: {star.spectral_type}</div>
            <div>Distance: {star.distance_ly.toFixed(1)} ly</div>
            <div>Temperature: ~{starProps.temperature.toLocaleString()} K</div>
            <div>Radius: {star.radius_solar?.toFixed(2) || '?'} R☉</div>
          </div>
        </div>
      </Html>
    </group>
  );
};

// Orbital trail component
interface OrbitalTrailProps {
  orbitalData: OrbitalData;
  color: string;
  opacity?: number;
}

const OrbitalTrail: React.FC<OrbitalTrailProps> = ({
  orbitalData,
  color,
  opacity = 0.3
}) => {
  const points = useMemo(() => {
    const trailPoints: THREE.Vector3[] = [];
    const segments = 128;

    for (let i = 0; i <= segments; i++) {
      const time = (i / segments) * orbitalData.orbitalPeriod;
      const position = calculateOrbitalPosition(orbitalData, time);
      trailPoints.push(new THREE.Vector3(...position));
    }

    return trailPoints;
  }, [orbitalData]);

  const geometry = useMemo(() => {
    const geometry = new THREE.BufferGeometry().setFromPoints(points);
    return geometry;
  }, [points]);

  return (
    <primitive object={new THREE.Line(geometry, new THREE.LineBasicMaterial({
      color,
      transparent: true,
      opacity
    }))} />
  );
};

// Time control component
interface TimeControlProps {
  timeSpeed: number;
  onTimeSpeedChange: (speed: number) => void;
  isPaused: boolean;
  onPauseToggle: () => void;
}

const TimeControl: React.FC<TimeControlProps> = ({
  timeSpeed,
  onTimeSpeedChange,
  isPaused,
  onPauseToggle
}) => {
  const speeds = [0.1, 0.5, 1, 2, 5, 10, 50, 100];

  return (
    <div className="absolute bottom-4 left-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-blue-400">
      <div className="text-sm font-bold mb-2 text-blue-400">Time Control</div>
      <div className="flex items-center space-x-2 mb-2">
        <button
          onClick={onPauseToggle}
          className={`px-3 py-1 rounded text-xs ${
            isPaused ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'
          }`}
        >
          {isPaused ? '▶ Play' : '⏸ Pause'}
        </button>
      </div>
      <div className="text-xs mb-1">Speed: {timeSpeed}x</div>
      <div className="flex flex-wrap gap-1">
        {speeds.map(speed => (
          <button
            key={speed}
            onClick={() => onTimeSpeedChange(speed)}
            className={`px-2 py-1 rounded text-xs ${
              timeSpeed === speed
                ? 'bg-blue-600 text-white'
                : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
            }`}
          >
            {speed}x
          </button>
        ))}
      </div>
    </div>
  );
};

// Main stunning solar system component
const StunningSolarSystemView: React.FC<StunningSolarSystemViewProps> = ({
  starId,
  onBack
}) => {
  const [starDetail, setStarDetail] = useState<StarDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeSpeed, setTimeSpeed] = useState(1);
  const [isPaused, setIsPaused] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [showOrbitalTrails, setShowOrbitalTrails] = useState(true);
  const [enablePostProcessing, setEnablePostProcessing] = useState(true);

  useEffect(() => {
    const loadStarDetail = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log(`🌟 StunningSolarSystemView: Loading star detail for ID ${starId}`);
        const detail = await stellarApi.getStarDetail(starId);
        setStarDetail(detail);
        console.log(`✅ StunningSolarSystemView: Loaded ${detail.name} with ${detail.planets.length} planets`);
      } catch (err) {
        console.error('❌ StunningSolarSystemView: Failed to load star detail:', err);
        setError(err instanceof Error ? err.message : 'Failed to load star system');
      } finally {
        setLoading(false);
      }
    };

    loadStarDetail();
  }, [starId]);

  // Time progression using useEffect instead of useFrame (which is only for Canvas children)
  useEffect(() => {
    if (isPaused) return;

    const interval = setInterval(() => {
      setCurrentTime(prev => prev + timeSpeed * 0.1);
    }, 16); // ~60fps

    return () => clearInterval(interval);
  }, [isPaused, timeSpeed]);

  // Generate orbital data for planets
  const planetOrbitalData = useMemo(() => {
    if (!starDetail?.planets) return [];

    return starDetail.planets.map((planet, index) => {
      const semiMajorAxis = (planet.sma_au || (index + 1)) * 4; // Scale for visibility
      const eccentricity = Math.random() * 0.1; // Small random eccentricity
      const inclination = (Math.random() - 0.5) * 0.2; // Small inclination
      const orbitalPeriod = Math.sqrt(Math.pow(semiMajorAxis, 3)) * 365.25; // Kepler's 3rd law

      return {
        semiMajorAxis,
        eccentricity,
        inclination,
        longitudeOfAscendingNode: Math.random() * Math.PI * 2,
        argumentOfPeriapsis: Math.random() * Math.PI * 2,
        meanAnomalyAtEpoch: Math.random() * Math.PI * 2,
        orbitalPeriod
      };
    });
  }, [starDetail]);

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-900 via-blue-900 to-black text-white">
        <div className="text-center">
          <div className="text-2xl mb-4 animate-pulse">🌌 Loading Solar System...</div>
          <div className="text-sm text-gray-400">Star ID: {starId}</div>
          <div className="mt-4">
            <div className="w-16 h-16 border-4 border-blue-400 border-t-transparent rounded-full animate-spin mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !starDetail) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-red-900 via-gray-900 to-black text-white">
        <div className="text-center">
          <div className="text-2xl mb-4 text-red-400">❌ Error Loading System</div>
          <div className="text-sm text-gray-400 mb-6">{error}</div>
          <button
            onClick={onBack}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
          >
            ← Back to Galaxy
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative bg-gradient-to-br from-indigo-900 via-purple-900 to-black">
      {/* Enhanced UI Controls */}
      <button
        onClick={onBack}
        className="absolute top-4 left-4 z-10 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors border border-blue-400"
      >
        ← Back to Galaxy
      </button>

      {/* System Information Panel */}
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-cyan-400">
        <h2 className="text-xl font-bold text-cyan-400 mb-2">{starDetail.name} System</h2>
        <div className="text-sm space-y-1">
          <div>🪐 {starDetail.planets.length} planets</div>
          <div>⭐ {starDetail.spectral_type} class star</div>
          <div>📏 {starDetail.distance_ly.toFixed(1)} light-years</div>
          <div>🌡️ {starDetail.mass_solar ? (starDetail.mass_solar * 5778).toLocaleString() : '?'} K</div>
        </div>

        {/* Visual Controls */}
        <div className="mt-4 pt-3 border-t border-gray-600">
          <div className="text-xs font-bold mb-2 text-cyan-400">Visual Options</div>
          <label className="flex items-center space-x-2 text-xs">
            <input
              type="checkbox"
              checked={showOrbitalTrails}
              onChange={(e) => setShowOrbitalTrails(e.target.checked)}
              className="rounded"
            />
            <span>Orbital Trails</span>
          </label>
          <label className="flex items-center space-x-2 text-xs mt-1">
            <input
              type="checkbox"
              checked={enablePostProcessing}
              onChange={(e) => setEnablePostProcessing(e.target.checked)}
              className="rounded"
            />
            <span>Post-Processing</span>
          </label>
        </div>
      </div>

      {/* Time Control */}
      <TimeControl
        timeSpeed={timeSpeed}
        onTimeSpeedChange={setTimeSpeed}
        isPaused={isPaused}
        onPauseToggle={() => setIsPaused(!isPaused)}
      />

      <Canvas
        camera={{ position: [20, 15, 20], fov: 60 }}
        style={{ background: 'transparent' }}
        gl={{
          preserveDrawingBuffer: true,
          antialias: true,
          alpha: true,
          powerPreference: "high-performance",
          toneMapping: THREE.ACESFilmicToneMapping,
          toneMappingExposure: 1.2
        }}
        onCreated={({ gl }) => {
          console.log('🎨 StunningSolarSystemView: WebGL context created');
          gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }}
      >
        {/* Enhanced Lighting Setup */}
        <ambientLight intensity={0.1} color="#1a1a2e" />
        <pointLight
          position={[0, 0, 0]}
          intensity={3}
          color="#FFD700"
          distance={100}
          decay={2}
        />

        {/* Background stars */}
        <Stars
          radius={200}
          depth={100}
          count={5000}
          factor={6}
          saturation={0.3}
          fade
          speed={0.1}
        />

        {/* Enhanced star */}
        <Suspense fallback={null}>
          <EnhancedStar star={starDetail} />
        </Suspense>

        {/* Planets with orbital mechanics */}
        {starDetail.planets.map((planet, index) => (
          <Suspense key={planet.planet_id} fallback={null}>
            <EnhancedPlanet
              planet={planet}
              orbitalData={planetOrbitalData[index]}
              time={currentTime}
              starPosition={[0, 0, 0]}
              onClick={() => {
                console.log(`🪐 Clicked planet: ${planet.name}`);
                // Future: Add planet detail modal
              }}
            />

            {/* Orbital trails */}
            {showOrbitalTrails && (
              <OrbitalTrail
                orbitalData={planetOrbitalData[index]}
                color={planet.in_habitable_zone ? "#00FF00" : "#666666"}
                opacity={planet.in_habitable_zone ? 0.6 : 0.3}
              />
            )}
          </Suspense>
        ))}

        {/* Enhanced camera controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={8}
          maxDistance={100}
          autoRotate={false}
          dampingFactor={0.05}
          enableDamping
          makeDefault
        />

        {/* Post-processing effects */}
        {enablePostProcessing && (
          <Suspense fallback={null}>
            <EffectComposer>
              <Bloom
                intensity={1.2}
                luminanceThreshold={0.1}
                luminanceSmoothing={0.9}
                radius={0.9}
              />
              <ChromaticAberration offset={[0.001, 0.001]} />
              <DepthOfField
                focusDistance={0.02}
                focalLength={0.05}
                bokehScale={3}
              />
              <Vignette eskil={false} offset={0.1} darkness={0.4} />
              <Noise opacity={0.015} />
              <ToneMapping adaptive={true} resolution={256} />
            </EffectComposer>
          </Suspense>
        )}
      </Canvas>

      {/* Performance and debug info */}
      <div className="absolute bottom-4 right-4 text-xs text-gray-400 bg-black bg-opacity-50 p-2 rounded">
        Time: {(currentTime / 365.25).toFixed(2)} years • Speed: {timeSpeed}x
        {isPaused && " (PAUSED)"}
      </div>
    </div>
  );
};

export default StunningSolarSystemView;
