import React, { useRef, useEffect, useState, useMemo } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Stars, Text, Html, Sparkles } from '@react-three/drei';
import * as THREE from 'three';
import { useGameStore } from '../store/gameStore';
import { simpleApiClient } from '../services/api-simple';
import { stellarApi } from '../services/stellarApi';
import type { Star } from '../types/stellar';

// Real stellar data will be loaded from the API

interface StarSystemProps {
  star: Star;
  position: [number, number, number];
  isSelected: boolean;
  hasFleets: boolean;
  onClick: () => void;
}

const StarSystem: React.FC<StarSystemProps> = ({ star, position, isSelected, hasFleets, onClick }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const glowRef = useRef<THREE.Mesh>(null);
  const [isHovered, setIsHovered] = useState(false);

  useFrame((state) => {
    try {
      if (meshRef.current) {
        meshRef.current.rotation.y += 0.01;
      }
      if (glowRef.current) {
        glowRef.current.rotation.z += 0.005;
        // Pulsing effect for selected systems
        if (isSelected && glowRef.current.scale && typeof glowRef.current.scale.setScalar === 'function') {
          glowRef.current.scale.setScalar(1 + Math.sin(state.clock.elapsedTime * 3) * 0.1);
        }
      }
    } catch (error) {
      // Silently ignore animation errors to prevent spam
    }
  });

  const starColor = stellarApi.getStarColor(star.spectral_type);
  const starSize = stellarApi.getStarSize(star);

  return (
    <group position={position}>
      {/* Invisible clickable area (larger than visual star) */}
      <mesh
        onClick={onClick}
        onPointerEnter={() => setIsHovered(true)}
        onPointerLeave={() => setIsHovered(false)}
      >
        <sphereGeometry args={[Math.max(starSize * 3, 1.5), 8, 8]} />
        <meshBasicMaterial transparent opacity={0} />
      </mesh>

      {/* Main star */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[starSize, 32, 32]} />
        <meshStandardMaterial
          color={starColor}
          emissive={starColor}
          emissiveIntensity={0.3}
        />
      </mesh>

      {/* Glow effect */}
      <mesh ref={glowRef} scale={2}>
        <sphereGeometry args={[starSize, 16, 16]} />
        <meshBasicMaterial
          color={starColor}
          transparent
          opacity={isSelected ? 0.4 : 0.2}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Selection ring */}
      {isSelected && (
        <mesh rotation={[Math.PI / 2, 0, 0]}>
          <ringGeometry args={[starSize * 2, starSize * 2.5, 32]} />
          <meshBasicMaterial
            color="#00FFFF"
            transparent
            opacity={0.8}
            side={THREE.DoubleSide}
          />
        </mesh>
      )}

      {/* Fleet indicator */}
      {hasFleets && (
        <mesh position={[0, starSize + 1, 0]}>
          <boxGeometry args={[0.3, 0.3, 0.3]} />
          <meshStandardMaterial color="#00FF00" emissive="#00FF00" emissiveIntensity={0.5} />
        </mesh>
      )}

      {/* Planet count indicator */}
      {star.planet_count > 0 && (
        <mesh position={[starSize + 0.5, 0, 0]}>
          <sphereGeometry args={[0.2, 8, 8]} />
          <meshStandardMaterial color="#4A90E2" emissive="#4A90E2" emissiveIntensity={0.3} />
        </mesh>
      )}

      {/* System label */}
      <Html distanceFactor={20} position={[0, starSize + 2, 0]}>
        <div className={`text-center pointer-events-none ${isSelected ? 'text-cyan-400' : 'text-white'}`}>
          <div className="text-sm font-bold drop-shadow-lg">{star.name}</div>
          {(isHovered || isSelected) && (
            <>
              <div className="text-xs text-gray-300">
                {star.spectral_type} • {star.distance_ly.toFixed(1)} ly
              </div>
              {star.planet_count > 0 && (
                <div className="text-xs text-blue-300">{star.planet_count} planets</div>
              )}
            </>
          )}
        </div>
      </Html>
    </group>
  );
};

interface FleetMarkerProps {
  fleet: any;
  systemPosition: [number, number, number];
}

const FleetMarker: React.FC<FleetMarkerProps> = ({ fleet, systemPosition }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    try {
      if (meshRef.current && meshRef.current.position) {
        meshRef.current.rotation.y += 0.02;
        if (systemPosition && systemPosition.length > 1) {
          meshRef.current.position.y = systemPosition[1] + 3 + Math.sin(state.clock.elapsedTime * 2) * 0.5;
        }
      }
    } catch (error) {
      // Silently ignore animation errors to prevent spam
    }
  });

  return (
    <mesh ref={meshRef} position={[systemPosition[0], systemPosition[1] + 3, systemPosition[2]]}>
      <coneGeometry args={[0.5, 1.5, 6]} />
      <meshStandardMaterial color="#00FF00" emissive="#00FF00" emissiveIntensity={0.3} />
    </mesh>
  );
};

interface HyperLaneProps {
  start: [number, number, number];
  end: [number, number, number];
}

const HyperLane: React.FC<HyperLaneProps> = ({ start, end }) => {
  const lineRef = useRef<THREE.Line>(null);

  const points = useMemo(() => {
    // Use deterministic offset based on start/end positions to avoid re-renders
    const seed = start[0] + start[1] + start[2] + end[0] + end[1] + end[2];
    const offsetX = (Math.sin(seed) * 2);
    const offsetY = (Math.cos(seed) * 2);
    const offsetZ = (Math.sin(seed * 1.5) * 2);

    const curve = new THREE.CatmullRomCurve3([
      new THREE.Vector3(...start),
      new THREE.Vector3(
        (start[0] + end[0]) / 2 + offsetX,
        (start[1] + end[1]) / 2 + offsetY,
        (start[2] + end[2]) / 2 + offsetZ
      ),
      new THREE.Vector3(...end)
    ]);
    return curve.getPoints(50);
  }, [start, end]);

  const geometry = useMemo(() => {
    const geometry = new THREE.BufferGeometry().setFromPoints(points);

    // Cleanup function to prevent memory leaks
    return geometry;
  }, [points]);

  // Cleanup geometry on unmount
  useEffect(() => {
    return () => {
      if (geometry) {
        geometry.dispose();
      }
    };
  }, [geometry]);

  useFrame((state) => {
    try {
      if (lineRef.current && lineRef.current.material && 'opacity' in lineRef.current.material) {
        // Animated opacity for energy flow effect
        const material = lineRef.current.material as THREE.LineBasicMaterial;
        if (material && typeof material.opacity === 'number') {
          material.opacity = 0.3 + Math.sin(state.clock.elapsedTime * 2) * 0.2;
        }
      }
    } catch (error) {
      // Silently ignore material access errors to prevent spam
    }
  });

  return (
    <>
      {/* Main hyperlane - using mesh with tube geometry for better compatibility */}
      <mesh ref={lineRef}>
        <tubeGeometry args={[new THREE.CatmullRomCurve3(points), 50, 0.02, 8, false]} />
        <meshBasicMaterial color="#4A90E2" transparent opacity={0.6} />
      </mesh>
    </>
  );
};

const Nebula: React.FC<{ position: [number, number, number]; color: string }> = ({ position, color }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    try {
      if (meshRef.current) {
        meshRef.current.rotation.x += 0.001;
        meshRef.current.rotation.y += 0.002;

        if (meshRef.current.material && 'opacity' in meshRef.current.material) {
          const material = meshRef.current.material as THREE.MeshStandardMaterial;
          if (material && typeof material.opacity === 'number') {
            material.opacity = 0.1 + Math.sin(state.clock.elapsedTime * 0.5) * 0.05;
          }
        }
      }
    } catch (error) {
      // Silently ignore material access errors to prevent spam
    }
  });

  return (
    <mesh ref={meshRef} position={position}>
      <sphereGeometry args={[8, 32, 32]} />
      <meshBasicMaterial
        color={color}
        transparent
        opacity={0.1}
        side={THREE.BackSide}
      />
    </mesh>
  );
};

const CameraController: React.FC<{ selectedSystemId?: string | null; displayStars: any[] }> = ({ selectedSystemId, displayStars }) => {
  const { camera, controls } = useThree();

  useEffect(() => {
    try {
      if (camera && camera.position && typeof camera.position.set === 'function') {
        camera.position.set(20, 15, 20);
      }
      if (camera && typeof camera.lookAt === 'function') {
        camera.lookAt(0, 0, 0);
      }
    } catch (error) {
      console.warn('Camera controller error:', error);
    }
  }, [camera]);

  // Handle camera transition when system is selected
  useEffect(() => {
    console.log('🎥 CameraController: selectedSystemId changed:', selectedSystemId);
    console.log('🎥 CameraController: displayStars length:', displayStars.length);

    if (selectedSystemId && displayStars.length > 0 && camera && controls) {
      try {
        console.log('🔍 CameraController: Looking for star with ID:', selectedSystemId);
        const selectedStar = displayStars.find(s => s.star_id.toString() === selectedSystemId);
        console.log('🌟 CameraController: Found star:', selectedStar?.name);

        if (selectedStar && selectedStar.position3D) {
          const [x, y, z] = selectedStar.position3D;
          console.log('🎯 CameraController: Moving camera to position:', [x, y, z]);

          // Smoothly transition camera to look at the selected system
          const targetPosition = new THREE.Vector3(x + 10, y + 8, z + 10);
          const targetLookAt = new THREE.Vector3(x, y, z);

          // Animate camera position
          const startPosition = camera.position.clone();
          const startTime = Date.now();
          const duration = 1500; // 1.5 seconds

          const animateCamera = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easeProgress = 1 - Math.pow(1 - progress, 3); // Ease out cubic

            camera.position.lerpVectors(startPosition, targetPosition, easeProgress);

            if (controls && (controls as any).target && typeof (controls as any).target.lerp === 'function') {
              (controls as any).target.lerp(targetLookAt, easeProgress);
              if (typeof (controls as any).update === 'function') {
                (controls as any).update();
              }
            }

            if (progress < 1) {
              requestAnimationFrame(animateCamera);
            }
          };

          animateCamera();
        }
      } catch (error) {
        console.warn('Camera transition error:', error);
      }
    }
  }, [selectedSystemId, displayStars, camera, controls]);

  return null;
};

const Galaxy3D: React.FC = () => {
  const [systemConnections, setSystemConnections] = useState<Map<string, string[]>>(new Map());
  const [stellarData, setStellarData] = useState<Star[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { fleets, systems, selectedSystemId, selectSystem } = useGameStore();

  console.log('🚀 Galaxy3D: Component rendered at', new Date().toISOString(), 'stellarData length:', stellarData.length);
  console.log('🌟 Galaxy3D: First 3 stars:', stellarData.slice(0, 3).map(s => `${s.name} (${s.distance_ly} ly)`));
  console.log('🎯 Galaxy3D: Selected system ID:', selectedSystemId);
  console.log('🎮 Galaxy3D: selectSystem function available:', typeof selectSystem);
  console.log('🔄 Galaxy3D: CACHE BUSTER v3.0 - Component updated with alerts!');

  // Log all star names to see what we're working with
  if (stellarData.length > 0) {
    console.log('🌟 Galaxy3D: All star names:', stellarData.map(s => s.name).join(', '));
  }

  // Load stellar data from API
  useEffect(() => {
    const loadStellarData = async () => {
      setIsLoading(true);
      try {
        console.log('🌌 Galaxy3D: Loading stellar data...');

        // Let stellarApi handle all timeouts and fallbacks
        const response = await stellarApi.getStars(20, 100); // Get stars within 20 light-years

        console.log('📊 Galaxy3D: Raw API response:', response);
        setStellarData(response.stars);

        // Determine if this is real data or fallback data
        const isRealData = response.stars.length > 8;
        if (isRealData) {
          console.log('✅ Galaxy3D: REAL stellar database loaded:', response.stars.length, 'stars');
        } else {
          console.log('⚠️ Galaxy3D: Using FALLBACK data:', response.stars.length, 'stars (limited set)');
        }
        console.log('🌟 Galaxy3D: First 3 stars:', response.stars.slice(0, 3).map(s => `${s.name} (${s.distance_ly} ly)`));
      } catch (error) {
        console.error('❌ Galaxy3D: Unexpected error loading stellar data:', error);
        // This should never happen since stellarApi.getStars() always returns data
        // But just in case, set empty array
        setStellarData([]);
        console.log('🔄 Galaxy3D: Set empty stellar data as emergency fallback');
      } finally {
        setIsLoading(false);
      }
    };

    loadStellarData();
  }, []);

  // Convert stellar data to 3D positions
  const displayStars = useMemo(() => {
    const timestamp = new Date().toISOString();
    console.log(`🔄 Galaxy3D [${timestamp}]: Converting stellar data to 3D positions...`);
    console.log(`📊 Galaxy3D [${timestamp}]: Input stellarData:`, stellarData.length, 'stars');

    const converted = stellarData.map(star => ({
      ...star,
      position3D: stellarApi.convertToCartesian(star)
    }));

    console.log('🗺️ Galaxy3D: Converted stars to 3D positions:', converted.length);
    if (converted.length > 0) {
      console.log('📍 Galaxy3D: Sample positions:', converted.slice(0, 3).map(s => `${s.name}: [${s.position3D.map(p => p.toFixed(1)).join(', ')}]`));
      console.log('🎯 Galaxy3D: All star names:', converted.map(s => s.name).slice(0, 10));
    } else {
      console.warn('⚠️ Galaxy3D: No stars converted! stellarData:', stellarData);
    }

    return converted;
  }, [stellarData]);

  // Create connections between nearby stars (simplified for now)
  useEffect(() => {
    const connections = new Map<string, string[]>();

    displayStars.forEach(star => {
      const nearbyStars = displayStars
        .filter(other => other.star_id !== star.star_id && other.distance_ly <= star.distance_ly + 5)
        .slice(0, 3) // Limit to 3 nearest neighbors
        .map(other => other.star_id.toString());

      connections.set(star.star_id.toString(), nearbyStars);
    });

    setSystemConnections(connections);
  }, [displayStars]);

  // Get fleets by system (map to star IDs)
  const fleetsBySystem = useMemo(() => {
    const bySystem = new Map<string, any[]>();
    try {
      if (Array.isArray(fleets)) {
        fleets.forEach(fleet => {
          try {
            // For now, map system IDs to star IDs (this would need proper mapping in production)
            if (fleet && fleet.system_id && typeof fleet.system_id === 'string') {
              const starId = fleet.system_id.replace('sys-', '');
              const systemFleets = bySystem.get(starId) || [];
              systemFleets.push(fleet);
              bySystem.set(starId, systemFleets);
            }
          } catch (fleetError) {
            console.warn('Error processing fleet:', fleet, fleetError);
          }
        });
      }
    } catch (error) {
      console.warn('Error processing fleets:', error);
    }
    return bySystem;
  }, [fleets]);

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-white text-xl">Loading stellar database...</div>
      </div>
    );
  }

  return (
    <div data-testid="galaxy-view" className="w-full h-full relative">
      {/* DEBUG: Show stellar data count */}
      <div className="absolute top-4 left-4 z-50 bg-black bg-opacity-75 text-white p-2 rounded">
        <div>Stars loaded: {stellarData.length}</div>
        <div>Cache buster: v4.0</div>
        {stellarData.length > 0 && (
          <div className="text-xs">
            First star: {stellarData[0]?.name}
          </div>
        )}
      </div>

      <Canvas
        camera={{ position: [20, 15, 20], fov: 60 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.3} />
        <pointLight position={[0, 0, 0]} intensity={1.5} color="#FFD700" />
        <pointLight position={[20, 20, 20]} intensity={0.5} color="#4A90E2" />
        <pointLight position={[-20, -20, -20]} intensity={0.5} color="#FF6B9D" />

        {/* Background stars */}
        <Stars
          radius={100}
          depth={50}
          count={5000}
          factor={4}
          saturation={0}
          fade
          speed={0.5}
        />

        {/* Nebulae */}
        <Nebula position={[-15, 5, -10]} color="#FF6B9D" />
        <Nebula position={[18, -8, 12]} color="#4ECDC4" />
        <Nebula position={[5, 12, -15]} color="#45B7D1" />

        {/* Cosmic dust and sparkles */}
        <Sparkles
          count={200}
          scale={[40, 40, 40]}
          size={2}
          speed={0.3}
          opacity={0.6}
          color="#FFD700"
        />

        {/* Camera controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={200}
          autoRotate={false}
          enableDamping={true}
          dampingFactor={0.05}
          zoomSpeed={0.5}
          panSpeed={0.8}
          rotateSpeed={0.4}
        />
        
        <CameraController selectedSystemId={selectedSystemId} displayStars={displayStars} />

        {/* Galactic Core */}
        <mesh position={[0, 0, 0]}>
          <sphereGeometry args={[0.5, 32, 32]} />
          <meshStandardMaterial
            color="#000000"
            emissive="#4A0080"
            emissiveIntensity={0.8}
          />
        </mesh>

        {/* Galactic Core Accretion Disk */}
        <mesh position={[0, 0, 0]} rotation={[Math.PI / 2, 0, 0]}>
          <ringGeometry args={[1, 3, 64]} />
          <meshBasicMaterial
            color="#FFD700"
            transparent
            opacity={0.3}
            side={THREE.DoubleSide}
          />
        </mesh>

        {/* Hyperlanes */}
        {Array.isArray(displayStars) && displayStars.map(star => {
          try {
            if (!star || !star.star_id || !star.position3D) return null;
            const neighbors = systemConnections.get(star.star_id.toString()) || [];
            return neighbors.map(neighborId => {
              try {
                const neighbor = displayStars.find(s => s && s.star_id && s.star_id.toString() === neighborId);
                if (!neighbor || !neighbor.position3D || star.star_id >= parseInt(neighborId)) return null; // Avoid duplicate lines

                return (
                  <HyperLane
                    key={`${star.star_id}-${neighborId}`}
                    start={star.position3D}
                    end={neighbor.position3D}
                  />
                );
              } catch (error) {
                console.warn('Error rendering hyperlane:', error);
                return null;
              }
            });
          } catch (error) {
            console.warn('Error processing star for hyperlanes:', error);
            return null;
          }
        })}

        {/* Star systems */}
        {(() => {
          console.log('🌟 Galaxy3D: Rendering stars. displayStars length:', displayStars.length);
          console.log('🎯 Galaxy3D: displayStars is array:', Array.isArray(displayStars));
          if (displayStars.length > 0) {
            console.log('🌟 Galaxy3D: First star for rendering:', displayStars[0]);
          }
          return null;
        })()}
        {Array.isArray(displayStars) && displayStars.map(star => {
          try {
            if (!star || !star.star_id || !star.position3D) {
              console.log('⚠️ Galaxy3D: Skipping invalid star:', star);
              return null;
            }
            return (
              <StarSystem
                key={star.star_id}
                star={star}
                position={star.position3D}
                isSelected={selectedSystemId === star.star_id.toString()}
                hasFleets={(fleetsBySystem.get(star.star_id.toString()) || []).length > 0}
                onClick={() => {
                  const timestamp = new Date().toISOString();
                  console.log(`🎯 Galaxy3D [${timestamp}]: Star clicked:`, star.name, star.star_id);
                  alert(`Star clicked: ${star.name} (ID: ${star.star_id}) - Time: ${timestamp}`);
                  selectSystem(star.star_id.toString());
                }}
              />
            );
          } catch (error) {
            console.warn('Error rendering star system:', error);
            return null;
          }
        })}

        {/* Fleet markers */}
        {Array.isArray(fleets) && fleets.map(fleet => {
          try {
            if (!fleet || !fleet.system_id || !fleet.id) return null;
            const starId = fleet.system_id.replace('sys-', '');
            const star = displayStars.find(s => s && s.star_id && s.star_id.toString() === starId);
            if (!star || !star.position3D) return null;

            return (
              <FleetMarker
                key={fleet.id}
                fleet={fleet}
                systemPosition={star.position3D}
              />
            );
          } catch (error) {
            console.warn('Error rendering fleet marker:', error);
            return null;
          }
        })}
      </Canvas>

      {/* UI Overlay */}
      <div className="absolute top-4 left-4 text-white">
        <div className="glass-panel p-4 backdrop-blur-md">
          <h3 className="text-lg font-bold mb-2 text-cyan-400">🌌 Galaxy Navigation</h3>
          <div className="text-sm space-y-1">
            <div className="flex items-center gap-2">
              <span className="text-cyan-300">🖱️</span>
              <span>Drag to rotate view</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-cyan-300">🔍</span>
              <span>Scroll to zoom in/out</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-cyan-300">🎯</span>
              <span>Click stars to select</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-cyan-300">🔄</span>
              <span>Auto-rotation enabled</span>
            </div>
          </div>
        </div>
      </div>

      {/* Selected system info */}
      {selectedSystemId && (
        <div className="absolute bottom-4 left-4 text-white">
          <div className="glass-panel p-4 backdrop-blur-md border border-cyan-400/30">
            {(() => {
              const selectedStar = displayStars.find(s => s.star_id.toString() === selectedSystemId);
              if (!selectedStar) return null;

              return (
                <>
                  <h3 className="text-lg font-bold mb-2 text-cyan-400">
                    ⭐ {selectedStar.name}
                  </h3>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-300">Distance:</span>
                      <span className="text-white font-mono">{selectedStar.distance_ly.toFixed(2)} ly</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Spectral Type:</span>
                      <span className="text-yellow-400 font-bold">{selectedStar.spectral_type || 'Unknown'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Planets:</span>
                      <span className="text-blue-400 font-bold">{selectedStar.planet_count}</span>
                    </div>
                    {selectedStar.mass_solar && (
                      <div className="flex justify-between">
                        <span className="text-gray-300">Mass:</span>
                        <span className="text-orange-400">{selectedStar.mass_solar.toFixed(2)} M☉</span>
                      </div>
                    )}
                    {selectedStar.teff_k && (
                      <div className="flex justify-between">
                        <span className="text-gray-300">Temperature:</span>
                        <span className="text-red-400">{selectedStar.teff_k.toLocaleString()} K</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-300">Fleet Count:</span>
                      <span className="text-green-400 font-bold">{(fleetsBySystem.get(selectedSystemId) || []).length}</span>
                    </div>
                    <div className="mt-3 pt-2 border-t border-gray-600">
                      <div className="text-xs text-gray-400">
                        Real astronomical data from stellar database
                      </div>
                    </div>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}

      {/* Galaxy stats overlay */}
      <div className="absolute top-4 right-4 text-white">
        <div data-testid="galaxy-stats" className="glass-panel p-4 backdrop-blur-md">
          <h3 className="text-lg font-bold mb-2 text-cyan-400">🌌 Stellar Neighborhood</h3>
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-300">Stars Loaded:</span>
              <span className="text-yellow-400 font-bold">{displayStars.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Total Planets:</span>
              <span className="text-blue-400 font-bold">{displayStars.reduce((sum, star) => sum + Number(star.planet_count || 0), 0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">M-Dwarfs:</span>
              <span className="text-red-400 font-bold">{displayStars.filter(s => s.spectral_type?.startsWith('M')).length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">G-Type Stars:</span>
              <span className="text-yellow-400 font-bold">{displayStars.filter(s => s.spectral_type?.startsWith('G')).length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Max Distance:</span>
              <span className="text-purple-400 font-bold">
                {Math.max(...displayStars.map(s => s.distance_ly)).toFixed(1)} ly
              </span>
            </div>
            <div className="mt-2 pt-2 border-t border-gray-600">
              <div className="text-xs text-gray-400">
                Real data from Gaia DR3 & NASA
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Galaxy3D;
