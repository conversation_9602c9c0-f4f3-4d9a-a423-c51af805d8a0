import React, { useRef, useEffect, useState, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Float, Text, Html } from '@react-three/drei';
import * as THREE from 'three';
import { stellarApi } from '../services/stellarApi';
import type { StarDetail, Planet } from '../types/stellar';

interface SolarSystemViewProps {
  starId: number;
  onBack: () => void;
}



interface OrbitingPlanetProps {
  planet: Planet;
  orbitalRadius: number;
  orbitalPeriod: number;
  time: number;
  onClick: () => void;
}

const OrbitingPlanet: React.FC<OrbitingPlanetProps> = ({
  planet,
  orbitalRadius,
  orbitalPeriod,
  time,
  onClick
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const groupRef = useRef<THREE.Group>(null);
  const [hovered, setHovered] = useState(false);

  // Calculate planet size based on radius (Earth = 1.0)
  const planetSize = Math.max(0.1, Math.min(2.0, (planet.radius_earth || 0.5) * 0.3));

  // Get planet color based on composition
  const getPlanetColor = (composition: string): string => {
    switch (composition.toLowerCase()) {
      case 'rocky': return '#8B4513';
      case 'gas_giant': return '#FFA500';
      case 'ice_giant': return '#4169E1';
      case 'super_earth': return '#228B22';
      default: return '#696969';
    }
  };

  const planetColor = getPlanetColor(planet.composition);

  // Calculate orbital position
  const angle = (time / orbitalPeriod) * Math.PI * 2;
  const x = Math.cos(angle) * orbitalRadius;
  const z = Math.sin(angle) * orbitalRadius;

  useFrame((state) => {
    if (meshRef.current) {
      // Planet rotation on its axis
      meshRef.current.rotation.y += 0.01;
    }

    if (groupRef.current) {
      // Update orbital position
      const currentAngle = (state.clock.elapsedTime / orbitalPeriod) * Math.PI * 2;
      groupRef.current.position.x = Math.cos(currentAngle) * orbitalRadius;
      groupRef.current.position.z = Math.sin(currentAngle) * orbitalRadius;
    }
  });

  return (
    <>
      {/* Orbital ring */}
      <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0]}>
        <ringGeometry args={[orbitalRadius - 0.02, orbitalRadius + 0.02, 128]} />
        <meshBasicMaterial
          color={planet.in_habitable_zone ? "#00FF00" : "#444444"}
          transparent
          opacity={planet.in_habitable_zone ? 0.4 : 0.2}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* Orbiting planet */}
      <group ref={groupRef} position={[x, 0, z]}>
        <Float speed={0.5} rotationIntensity={0.1} floatIntensity={0.05}>
          <mesh
            ref={meshRef}
            onClick={onClick}
            onPointerEnter={() => setHovered(true)}
            onPointerLeave={() => setHovered(false)}
          >
            <sphereGeometry args={[planetSize, 32, 32]} />
            <meshStandardMaterial
              color={planetColor}
              emissive={planet.in_habitable_zone ? '#004400' : '#000000'}
              emissiveIntensity={planet.in_habitable_zone ? 0.3 : 0.1}
              roughness={0.7}
              metalness={0.2}
            />
          </mesh>
        </Float>

        {/* Planet label */}
        {hovered && (
          <Html position={[0, planetSize + 0.5, 0]} center>
            <div className="bg-black bg-opacity-90 text-white px-3 py-2 rounded text-sm border border-gray-600">
              <div className="font-bold text-yellow-300">{planet.name}</div>
              <div>Mass: {planet.mass_earth?.toFixed(2) || '?'} Earth</div>
              <div>Distance: {planet.sma_au?.toFixed(2) || '?'} AU</div>
              <div>Type: {planet.composition}</div>
              {planet.in_habitable_zone && (
                <div className="text-green-400 font-semibold">🌍 Habitable Zone</div>
              )}
            </div>
          </Html>
        )}
      </group>
    </>
  );
};

interface StarComponentProps {
  star: StarDetail;
}

const StarComponent: React.FC<StarComponentProps> = ({ star }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const coronaRef = useRef<THREE.Mesh>(null);

  // Calculate star size based on radius (Sun = 1.0)
  const starSize = Math.max(0.8, Math.min(3.0, (star.radius_solar || 1.0) * 1.2));

  // Get star color based on spectral type
  const getStarColor = (spectralType?: string): string => {
    if (!spectralType) return '#FFFFFF';
    const type = spectralType.charAt(0).toUpperCase();
    switch (type) {
      case 'O': return '#9BB0FF'; // Blue
      case 'B': return '#AABFFF'; // Blue-white
      case 'A': return '#CAD7FF'; // White
      case 'F': return '#F8F7FF'; // Yellow-white
      case 'G': return '#FFF4EA'; // Yellow (like our Sun)
      case 'K': return '#FFD2A1'; // Orange
      case 'M': return '#FFAD51'; // Red
      default: return '#FFFFFF';
    }
  };

  const starColor = getStarColor(star.spectral_type);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.005;
    }

    if (coronaRef.current) {
      // Pulsing corona effect
      const pulse = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.1;
      coronaRef.current.scale.setScalar(starSize * 1.5 * pulse);
    }
  });

  return (
    <group position={[0, 0, 0]}>
      {/* Main star */}
      <Float speed={0.2} rotationIntensity={0.05} floatIntensity={0.02}>
        <mesh ref={meshRef}>
          <sphereGeometry args={[starSize, 64, 64]} />
          <meshStandardMaterial
            color={starColor}
            emissive={starColor}
            emissiveIntensity={0.8}
            roughness={0}
            metalness={0}
          />
        </mesh>
      </Float>

      {/* Star corona layers */}
      <mesh ref={coronaRef} scale={starSize * 1.5}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color={starColor}
          transparent
          opacity={0.15}
          side={THREE.BackSide}
        />
      </mesh>

      <mesh scale={starSize * 2.2}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color={starColor}
          transparent
          opacity={0.05}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Star label */}
      <Html position={[0, starSize + 1.5, 0]} center>
        <div className="bg-black bg-opacity-90 text-white px-4 py-3 rounded-lg border border-yellow-600">
          <div className="font-bold text-xl text-yellow-300">{star.name}</div>
          <div className="text-sm text-gray-300">{star.spectral_type}</div>
          <div className="text-sm text-gray-300">{star.distance_ly.toFixed(1)} ly</div>
        </div>
      </Html>
    </group>
  );
};

const SolarSystemView: React.FC<SolarSystemViewProps> = ({ starId, onBack }) => {
  const [starDetail, setStarDetail] = useState<StarDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeSpeed, setTimeSpeed] = useState(1);
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    const loadStarDetail = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log(`🌟 SolarSystemView: Loading star detail for ID ${starId}`);
        const detail = await stellarApi.getStarDetail(starId);
        setStarDetail(detail);
        console.log(`✅ SolarSystemView: Loaded ${detail.name} with ${detail.planets.length} planets`);
      } catch (err) {
        console.error('❌ SolarSystemView: Failed to load star detail:', err);
        setError(err instanceof Error ? err.message : 'Failed to load star system');
      } finally {
        setLoading(false);
      }
    };

    loadStarDetail();

    // Cleanup function
    return () => {
      console.log('🧹 SolarSystemView: Cleaning up component');
      setStarDetail(null);
      setError(null);
    };
  }, [starId]);

  // Calculate orbital data for planets
  const planetOrbitalData = useMemo(() => {
    if (!starDetail?.planets) return [];

    return starDetail.planets.map((planet, index) => {
      const orbitalRadius = Math.max(2, (planet.sma_au || (index + 1)) * 4); // Scale for visibility
      const orbitalPeriod = Math.sqrt(Math.pow(planet.sma_au || (index + 1), 3)) * 2; // Simplified Kepler's law

      return {
        planet,
        orbitalRadius,
        orbitalPeriod: Math.max(5, orbitalPeriod), // Minimum period for visibility
      };
    });
  }, [starDetail]);

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <div className="text-xl mb-2">Loading Solar System...</div>
          <div className="text-sm text-gray-400">Star ID: {starId}</div>
        </div>
      </div>
    );
  }

  if (error || !starDetail) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <div className="text-xl mb-2 text-red-400">Error Loading System</div>
          <div className="text-sm text-gray-400 mb-4">{error}</div>
          <button
            onClick={onBack}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded"
          >
            Back to Galaxy
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative bg-black">
      {/* Back button */}
      <button
        onClick={onBack}
        className="absolute top-4 left-4 z-10 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
      >
        ← Back to Galaxy
      </button>

      {/* System info */}
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-gray-600">
        <h2 className="text-xl font-bold text-yellow-300">{starDetail.name} System</h2>
        <div className="text-sm text-gray-300">
          <div>🪐 {starDetail.planets.length} planets</div>
          <div>⭐ {starDetail.spectral_type} star</div>
          <div>📏 {starDetail.distance_ly.toFixed(1)} light-years</div>
        </div>
      </div>

      {/* Time controls */}
      <div className="absolute bottom-4 left-4 z-10 bg-black bg-opacity-90 text-white p-3 rounded-lg border border-gray-600">
        <div className="text-sm font-bold mb-2">Time Control</div>
        <div className="flex items-center gap-2 mb-2">
          <button
            onClick={() => setIsPaused(!isPaused)}
            className="px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs"
          >
            {isPaused ? '▶️' : '⏸️'}
          </button>
          <span className="text-xs">Speed: {timeSpeed}x</span>
        </div>
        <div className="flex gap-1">
          {[0.1, 0.5, 1, 2, 5, 10].map(speed => (
            <button
              key={speed}
              onClick={() => setTimeSpeed(speed)}
              className={`px-2 py-1 rounded text-xs ${
                timeSpeed === speed
                  ? 'bg-yellow-600 text-black'
                  : 'bg-gray-600 hover:bg-gray-500'
              }`}
            >
              {speed}x
            </button>
          ))}
        </div>
      </div>

      <Canvas
        camera={{ position: [20, 15, 20], fov: 60 }}
        style={{ background: 'transparent' }}
        gl={{
          preserveDrawingBuffer: true,
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        onCreated={({ gl }) => {
          console.log('🎨 SolarSystemView: WebGL context created');
          gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }}
      >
        {/* Enhanced lighting */}
        <ambientLight intensity={0.4} color="#ffffff" />
        <pointLight
          position={[0, 0, 0]}
          intensity={3}
          color={starDetail ? starDetail.spectral_type?.charAt(0) === 'M' ? "#FFAD51" : "#FFD700" : "#FFD700"}
          distance={100}
          decay={2}
        />
        <directionalLight
          position={[10, 10, 5]}
          intensity={0.5}
          color="#ffffff"
          castShadow
        />

        {/* Controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={8}
          maxDistance={80}
          autoRotate={false}
          dampingFactor={0.05}
          enableDamping={true}
        />

        {/* Star */}
        {starDetail && <StarComponent star={starDetail} />}

        {/* Orbiting Planets */}
        {planetOrbitalData.map((data, index) => (
          <OrbitingPlanet
            key={data.planet.planet_id}
            planet={data.planet}
            orbitalRadius={data.orbitalRadius}
            orbitalPeriod={data.orbitalPeriod}
            time={isPaused ? 0 : timeSpeed}
            onClick={() => {
              console.log(`🪐 Clicked planet: ${data.planet.name}`);
              // Future: Add planet detail view
            }}
          />
        ))}
      </Canvas>
    </div>
  );
};

export default SolarSystemView;
