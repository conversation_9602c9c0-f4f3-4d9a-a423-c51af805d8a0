import React, { useRef, useEffect, useState, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Float, Text, Html } from '@react-three/drei';
import * as THREE from 'three';
import { stellarApi } from '../services/stellarApi';
import type { StarDetail, Planet } from '../types/stellar';

interface SolarSystemViewProps {
  starId: number;
  onBack: () => void;
}

interface PlanetProps {
  planet: Planet;
  position: [number, number, number];
  onClick: () => void;
}

const PlanetComponent: React.FC<PlanetProps> = ({ planet, position, onClick }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // Calculate planet size based on radius (Earth = 1.0)
  const planetSize = Math.max(0.1, Math.min(2.0, (planet.radius_earth || 0.5) * 0.3));
  
  // Get planet color based on composition
  const getPlanetColor = (composition: string): string => {
    switch (composition.toLowerCase()) {
      case 'rocky': return '#8B4513';
      case 'gas_giant': return '#FFA500';
      case 'ice_giant': return '#4169E1';
      case 'super_earth': return '#228B22';
      default: return '#696969';
    }
  };

  const planetColor = getPlanetColor(planet.composition);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
    }
  });

  return (
    <group position={position}>
      {/* Planet */}
      <Float speed={0.5} rotationIntensity={0.1} floatIntensity={0.05}>
        <mesh
          ref={meshRef}
          onClick={onClick}
          onPointerEnter={() => setHovered(true)}
          onPointerLeave={() => setHovered(false)}
        >
          <sphereGeometry args={[planetSize, 32, 32]} />
          <meshStandardMaterial
            color={planetColor}
            emissive={planet.in_habitable_zone ? '#004400' : '#000000'}
            emissiveIntensity={planet.in_habitable_zone ? 0.2 : 0}
            roughness={0.8}
            metalness={0.1}
          />
        </mesh>
      </Float>

      {/* Planet label */}
      {hovered && (
        <Html position={[0, planetSize + 0.5, 0]} center>
          <div className="bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
            <div className="font-bold">{planet.name}</div>
            <div>Mass: {planet.mass_earth?.toFixed(2) || '?'} Earth</div>
            <div>Distance: {planet.sma_au?.toFixed(2) || '?'} AU</div>
            <div>Type: {planet.composition}</div>
            {planet.in_habitable_zone && (
              <div className="text-green-400">Habitable Zone</div>
            )}
          </div>
        </Html>
      )}

      {/* Orbital path */}
      <mesh rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[position[0] - 0.05, position[0] + 0.05, 64]} />
        <meshBasicMaterial
          color="#444444"
          transparent
          opacity={0.3}
          side={THREE.DoubleSide}
        />
      </mesh>
    </group>
  );
};

interface StarComponentProps {
  star: StarDetail;
}

const StarComponent: React.FC<StarComponentProps> = ({ star }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  
  // Calculate star size based on radius (Sun = 1.0)
  const starSize = Math.max(0.5, Math.min(4.0, (star.radius_solar || 1.0) * 0.8));
  
  // Get star color based on spectral type
  const getStarColor = (spectralType?: string): string => {
    if (!spectralType) return '#FFFFFF';
    const type = spectralType.charAt(0).toUpperCase();
    switch (type) {
      case 'O': return '#9BB0FF'; // Blue
      case 'B': return '#AABFFF'; // Blue-white
      case 'A': return '#CAD7FF'; // White
      case 'F': return '#F8F7FF'; // Yellow-white
      case 'G': return '#FFF4EA'; // Yellow (like our Sun)
      case 'K': return '#FFD2A1'; // Orange
      case 'M': return '#FFAD51'; // Red
      default: return '#FFFFFF';
    }
  };

  const starColor = getStarColor(star.spectral_type);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.005;
    }
  });

  return (
    <group position={[0, 0, 0]}>
      {/* Main star */}
      <Float speed={0.2} rotationIntensity={0.05} floatIntensity={0.02}>
        <mesh ref={meshRef}>
          <sphereGeometry args={[starSize, 64, 64]} />
          <meshStandardMaterial
            color={starColor}
            emissive={starColor}
            emissiveIntensity={0.6}
          />
        </mesh>
      </Float>

      {/* Star corona */}
      <mesh scale={starSize * 2}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color={starColor}
          transparent
          opacity={0.1}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Star label */}
      <Html position={[0, starSize + 1, 0]} center>
        <div className="bg-black bg-opacity-75 text-white px-3 py-2 rounded">
          <div className="font-bold text-lg">{star.name}</div>
          <div className="text-sm">{star.spectral_type}</div>
          <div className="text-sm">{star.distance_ly.toFixed(1)} ly</div>
        </div>
      </Html>
    </group>
  );
};

const SolarSystemView: React.FC<SolarSystemViewProps> = ({ starId, onBack }) => {
  const [starDetail, setStarDetail] = useState<StarDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadStarDetail = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log(`🌟 SolarSystemView: Loading star detail for ID ${starId}`);
        const detail = await stellarApi.getStarDetail(starId);
        setStarDetail(detail);
        console.log(`✅ SolarSystemView: Loaded ${detail.name} with ${detail.planets.length} planets`);
      } catch (err) {
        console.error('❌ SolarSystemView: Failed to load star detail:', err);
        setError(err instanceof Error ? err.message : 'Failed to load star system');
      } finally {
        setLoading(false);
      }
    };

    loadStarDetail();

    // Cleanup function
    return () => {
      console.log('🧹 SolarSystemView: Cleaning up component');
      setStarDetail(null);
      setError(null);
    };
  }, [starId]);

  // Calculate planet positions based on semi-major axis
  const planetPositions = useMemo(() => {
    if (!starDetail?.planets) return [];
    
    return starDetail.planets.map((planet, index) => {
      const distance = (planet.sma_au || (index + 1)) * 3; // Scale for visibility
      const angle = (index / starDetail.planets.length) * Math.PI * 2;
      const x = Math.cos(angle) * distance;
      const z = Math.sin(angle) * distance;
      return [x, 0, z] as [number, number, number];
    });
  }, [starDetail]);

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <div className="text-xl mb-2">Loading Solar System...</div>
          <div className="text-sm text-gray-400">Star ID: {starId}</div>
        </div>
      </div>
    );
  }

  if (error || !starDetail) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <div className="text-xl mb-2 text-red-400">Error Loading System</div>
          <div className="text-sm text-gray-400 mb-4">{error}</div>
          <button
            onClick={onBack}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded"
          >
            Back to Galaxy
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative bg-black">
      {/* Back button */}
      <button
        onClick={onBack}
        className="absolute top-4 left-4 z-10 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
      >
        ← Back to Galaxy
      </button>

      {/* System info */}
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-75 text-white p-4 rounded">
        <h2 className="text-xl font-bold">{starDetail.name} System</h2>
        <div className="text-sm text-gray-300">
          <div>{starDetail.planets.length} planets</div>
          <div>{starDetail.spectral_type} star</div>
          <div>{starDetail.distance_ly.toFixed(1)} light-years</div>
        </div>
      </div>

      <Canvas
        camera={{ position: [15, 10, 15], fov: 60 }}
        style={{ background: 'transparent' }}
        gl={{
          preserveDrawingBuffer: true,
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        onCreated={({ gl }) => {
          console.log('🎨 SolarSystemView: WebGL context created');
          gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.2} />
        <pointLight position={[0, 0, 0]} intensity={2} color="#FFD700" />

        {/* Controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={50}
          autoRotate={false}
        />

        {/* Star */}
        {starDetail && <StarComponent star={starDetail} />}

        {/* Planets */}
        {starDetail.planets.map((planet, index) => (
          <PlanetComponent
            key={planet.planet_id}
            planet={planet}
            position={planetPositions[index]}
            onClick={() => {
              console.log(`🪐 Clicked planet: ${planet.name}`);
              // Future: Add planet detail view
            }}
          />
        ))}
      </Canvas>
    </div>
  );
};

export default SolarSystemView;
