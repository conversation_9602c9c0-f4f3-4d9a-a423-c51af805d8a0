import React, { useRef, useEffect, useState, useMemo, Suspense } from 'react';
import { Can<PERSON>, useFrame, useThree, extend } from '@react-three/fiber';
import {
  OrbitControls,
  Stars,
  Text,
  Html,
  Sparkles,
  Sphere,
  Ring,
  Torus,
  useTexture,
  shaderMaterial,
  Billboard,
  Trail,
  Float,
  MeshDistortMaterial,
  Environment
} from '@react-three/drei';
import {
  EffectComposer,
  Bloom,
  ChromaticAberration,
  Vignette,
  Noise,
  ToneMapping
} from '@react-three/postprocessing';
import * as THREE from 'three';
import { useGameStore } from '../store/gameStore';
import { stellarApi } from '../services/stellarApi';
import type { Star } from '../types/stellar';
import SolarSystemView from './SolarSystemView';

// Custom shader materials for stunning effects
const StarCoronaMaterial = shaderMaterial(
  {
    time: 0,
    color: new THREE.Color('#ffffff'),
    intensity: 1.0,
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    uniform float time;
    
    void main() {
      vUv = uv;
      vPosition = position;
      
      // Add subtle vertex displacement for corona effect
      vec3 newPosition = position;
      newPosition += normal * sin(time * 2.0 + position.x * 10.0) * 0.02;
      
      gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
    }
  `,
  // Fragment shader
  `
    uniform float time;
    uniform vec3 color;
    uniform float intensity;
    varying vec2 vUv;
    varying vec3 vPosition;
    
    void main() {
      vec2 center = vec2(0.5);
      float dist = distance(vUv, center);
      
      // Create corona effect
      float corona = 1.0 - smoothstep(0.0, 0.5, dist);
      corona = pow(corona, 2.0);
      
      // Add pulsing effect
      float pulse = sin(time * 3.0) * 0.3 + 0.7;
      corona *= pulse;
      
      // Add noise for realistic corona
      float noise = sin(vPosition.x * 20.0 + time) * sin(vPosition.y * 20.0 + time) * 0.1;
      corona += noise;
      
      vec3 finalColor = color * intensity;
      gl_FragColor = vec4(finalColor, corona * intensity);
    }
  `
);

const NebulaMaterial = shaderMaterial(
  {
    time: 0,
    color1: new THREE.Color('#ff6b9d'),
    color2: new THREE.Color('#4ecdc4'),
    opacity: 0.3,
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    
    void main() {
      vUv = uv;
      vPosition = position;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  // Fragment shader
  `
    uniform float time;
    uniform vec3 color1;
    uniform vec3 color2;
    uniform float opacity;
    varying vec2 vUv;
    varying vec3 vPosition;
    
    // Noise function
    float noise(vec3 p) {
      return sin(p.x * 10.0) * sin(p.y * 10.0) * sin(p.z * 10.0);
    }
    
    void main() {
      vec3 pos = vPosition + time * 0.1;
      
      // Multi-octave noise for realistic nebula
      float n1 = noise(pos * 2.0) * 0.5;
      float n2 = noise(pos * 4.0) * 0.25;
      float n3 = noise(pos * 8.0) * 0.125;
      float totalNoise = n1 + n2 + n3;
      
      // Color mixing based on noise
      vec3 finalColor = mix(color1, color2, totalNoise + 0.5);
      
      // Fade edges
      float edgeFade = 1.0 - length(vUv - 0.5) * 2.0;
      edgeFade = smoothstep(0.0, 1.0, edgeFade);
      
      float alpha = (totalNoise + 0.5) * opacity * edgeFade;
      gl_FragColor = vec4(finalColor, alpha);
    }
  `
);

// Extend Three.js with our custom materials
extend({ StarCoronaMaterial, NebulaMaterial });

// Declare module augmentation for TypeScript
declare module '@react-three/fiber' {
  interface ThreeElements {
    starCoronaMaterial: any;
    nebulaMaterial: any;
  }
}

interface StunningStarSystemProps {
  star: Star;
  position: [number, number, number];
  isSelected: boolean;
  hasFleets: boolean;
  onClick: () => void;
  onHover: (star: Star | null) => void;
}

const StunningStarSystem: React.FC<StunningStarSystemProps> = ({ 
  star, position, isSelected, hasFleets, onClick, onHover 
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const coronaRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.005;
    }
    if (coronaRef.current && coronaRef.current.material) {
      // Update corona shader time
      (coronaRef.current.material as any).time = state.clock.elapsedTime;
    }
  });

  const starColor = stellarApi.getStarColor(star.spectral_type);
  const starSize = Math.max(0.3, Math.min(2.0, stellarApi.getStarSize(star)));

  const handleClick = (e: any) => {
    e.stopPropagation();
    console.log('Star clicked:', star.name, star.star_id);
    onClick();
  };

  const handlePointerEnter = (e: any) => {
    e.stopPropagation();
    setHovered(true);
    onHover(star);
    document.body.style.cursor = 'pointer';
  };

  const handlePointerLeave = (e: any) => {
    e.stopPropagation();
    setHovered(false);
    onHover(null);
    document.body.style.cursor = 'auto';
  };

  return (
    <group position={position}>
      {/* Larger invisible clickable area for easier selection */}
      <mesh
        onClick={handleClick}
        onPointerEnter={handlePointerEnter}
        onPointerLeave={handlePointerLeave}
        visible={false}
      >
        <sphereGeometry args={[Math.max(starSize * 2, 1.5), 16, 16]} />
        <meshBasicMaterial transparent opacity={0} />
      </mesh>

      {/* Main star with enhanced materials */}
      <Float speed={1} rotationIntensity={0.1} floatIntensity={0.1}>
        <mesh ref={meshRef}>
          <sphereGeometry args={[starSize, 64, 64]} />
          <MeshDistortMaterial
            color={starColor}
            emissive={starColor}
            emissiveIntensity={hovered ? 0.8 : 0.5}
            distort={0.1}
            speed={2}
            roughness={0.1}
            metalness={0.1}
          />
        </mesh>
      </Float>

      {/* Corona effect */}
      <mesh ref={coronaRef} scale={starSize * 3}>
        <sphereGeometry args={[1, 32, 32]} />
        <starCoronaMaterial
          color={new THREE.Color(starColor)}
          intensity={isSelected ? 1.5 : hovered ? 1.2 : 0.8}
          transparent
          side={THREE.BackSide}
        />
      </mesh>

      {/* Selection ring with trail effect */}
      {isSelected && (
        <Trail width={0.5} length={20} color={starColor} attenuation={(t) => t * t}>
          <Ring args={[starSize * 2, starSize * 2.5, 64]}>
            <meshBasicMaterial color="#00FFFF" transparent opacity={0.8} side={THREE.DoubleSide} />
          </Ring>
        </Trail>
      )}

      {/* Planetary system visualization - removed for cleaner galaxy view */}

      {/* Fleet indicators with enhanced visuals */}
      {hasFleets && (
        <Billboard>
          <Sparkles count={10} scale={2} size={1} speed={0.5} color="#FFD700" />
        </Billboard>
      )}

      {/* Star name label */}
      {(hovered || isSelected) && (
        <Billboard position={[0, starSize + 1, 0]}>
          <Text
            fontSize={0.5}
            color="#FFFFFF"
            anchorX="center"
            anchorY="middle"
            outlineWidth={0.02}
            outlineColor="#000000"
          >
            {star.name}
          </Text>
        </Billboard>
      )}
    </group>
  );
};

interface VolumetricNebulaProps {
  position: [number, number, number];
  color1: string;
  color2: string;
  scale?: number;
}

const VolumetricNebula: React.FC<VolumetricNebulaProps> = ({ 
  position, color1, color2, scale = 10 
}) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current && meshRef.current.material) {
      (meshRef.current.material as any).time = state.clock.elapsedTime * 0.5;
    }
  });

  return (
    <mesh ref={meshRef} position={position} scale={scale}>
      <sphereGeometry args={[1, 32, 32]} />
      <nebulaMaterial
        color1={new THREE.Color(color1)}
        color2={new THREE.Color(color2)}
        opacity={0.4}
        transparent
        side={THREE.DoubleSide}
      />
    </mesh>
  );
};

const GalacticCore: React.FC = () => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
      meshRef.current.rotation.z += 0.005;
    }
  });

  return (
    <group position={[0, 0, 0]}>
      {/* Central black hole */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[0.8, 64, 64]} />
        <meshStandardMaterial
          color="#000000"
          emissive="#4A0080"
          emissiveIntensity={1.2}
          roughness={0}
          metalness={1}
        />
      </mesh>
      
      {/* Accretion disk */}
      <Torus args={[2, 0.1, 16, 100]} rotation={[Math.PI / 2, 0, 0]}>
        <MeshDistortMaterial
          color="#FF6B00"
          emissive="#FF6B00"
          emissiveIntensity={0.8}
          distort={0.3}
          speed={5}
          transparent
          opacity={0.7}
        />
      </Torus>
      
      {/* Energy jets */}
      <group>
        <mesh position={[0, 5, 0]} rotation={[0, 0, 0]}>
          <cylinderGeometry args={[0.1, 0.05, 10, 8]} />
          <meshBasicMaterial color="#00FFFF" transparent opacity={0.6} />
        </mesh>
        <mesh position={[0, -5, 0]} rotation={[Math.PI, 0, 0]}>
          <cylinderGeometry args={[0.1, 0.05, 10, 8]} />
          <meshBasicMaterial color="#00FFFF" transparent opacity={0.6} />
        </mesh>
      </group>
    </group>
  );
};

const CameraController: React.FC<{ displayStars: any[]; onResetCamera: () => void }> = ({ displayStars, onResetCamera }) => {
  const { camera, controls } = useThree();
  const { selectedSystemId } = useGameStore();

  // Reset camera to galactic overview
  const resetToGalacticCenter = () => {
    if (controls) {
      // Reset OrbitControls target to galactic center
      controls.target.set(0, 0, 0);

      // Move camera to overview position
      const startPosition = camera.position.clone();
      const endPosition = new THREE.Vector3(50, 30, 50);

      let progress = 0;
      const animateCamera = () => {
        progress += 0.03;
        if (progress <= 1) {
          camera.position.lerpVectors(startPosition, endPosition, progress);
          controls.update();
          requestAnimationFrame(animateCamera);
        }
      };
      animateCamera();
    }
  };

  // Expose reset function to parent
  useEffect(() => {
    if (onResetCamera && onResetCamera.current) {
      onResetCamera.current = resetToGalacticCenter;
    }
  }, [onResetCamera]);

  useEffect(() => {
    if (selectedSystemId && displayStars.length > 0 && controls) {
      // Find the selected star
      const selectedStar = displayStars.find(star => star.star_id === selectedSystemId);
      if (selectedStar && selectedStar.position) {
        const [x, y, z] = selectedStar.position;

        // Set OrbitControls target to the selected star
        const startTarget = controls.target.clone();
        const endTarget = new THREE.Vector3(x, y, z);

        // Calculate a good camera position relative to the star
        const currentDistance = camera.position.distanceTo(controls.target);
        const optimalDistance = Math.max(8, Math.min(20, currentDistance));

        const direction = camera.position.clone().sub(controls.target).normalize();
        const newCameraPosition = endTarget.clone().add(direction.multiplyScalar(optimalDistance));

        // Animate both camera position and target
        const startPosition = camera.position.clone();

        let progress = 0;
        const animateCamera = () => {
          progress += 0.03;
          if (progress <= 1) {
            // Interpolate target
            controls.target.lerpVectors(startTarget, endTarget, progress);

            // Interpolate camera position
            camera.position.lerpVectors(startPosition, newCameraPosition, progress);

            controls.update();
            requestAnimationFrame(animateCamera);
          }
        };
        animateCamera();
      }
    }
  }, [selectedSystemId, camera, controls, displayStars]);

  return null;
};

const StunningGalaxy3D: React.FC = () => {
  console.log('🌌 StunningGalaxy3D: Component initializing');
  console.log('🕐 StunningGalaxy3D: Init timestamp:', new Date().toISOString());

  const {
    fleets,
    selectedSystemId,
    selectSystem
  } = useGameStore();

  // View state management
  const [currentView, setCurrentView] = useState<'galaxy' | 'solar_system'>('galaxy');
  const [selectedStarId, setSelectedStarId] = useState<number | null>(null);

  const [stars, setStars] = useState<Star[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hoveredStar, setHoveredStar] = useState<Star | null>(null);
  const [enablePostProcessing, setEnablePostProcessing] = useState(true);
  const resetCameraRef = useRef<() => void>(() => {});

  // Load stellar data
  useEffect(() => {
    const loadStars = async () => {
      try {
        setIsLoading(true);
        const data = await stellarApi.getStars();
        setStars(data.stars);
        console.log('✅ Loaded stellar data for stunning galaxy:', data.stars.length, 'stars');
      } catch (error) {
        console.error('❌ Failed to load stellar data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadStars();
  }, []);

  // Convert stars to display format with 3D positions
  const displayStars = useMemo(() => {
    if (!stars || stars.length === 0) return [];

    return stars.map((star, index) => {
      // Special handling for Sol (our Sun) - place it in a specific location, not at center
      if (star.name === 'Sol' || star.distance_ly === 0) {
        return {
          ...star,
          position: [15, 2, 8] as [number, number, number] // Place Sol in a specific location
        };
      }

      // Create 3D spiral galaxy distribution for other stars
      // Adjust index to account for Sol being handled separately
      const adjustedIndex = index === 0 ? 1 : index; // Ensure we don't put another star at center
      const angle = (adjustedIndex / stars.length) * Math.PI * 8; // Multiple spiral arms
      const radius = 8 + (adjustedIndex / stars.length) * 25; // Start further from center, spread less
      const spiralTightness = 0.3;

      const x = Math.cos(angle + radius * spiralTightness) * radius;
      const z = Math.sin(angle + radius * spiralTightness) * radius;

      // Use deterministic Y position based on star ID to avoid repositioning on re-renders
      const seed = star.star_id * 0.1; // Create deterministic seed from star ID
      const y = (Math.sin(seed) * Math.cos(seed * 1.3)) * 4; // Deterministic vertical spread

      return {
        ...star,
        position: [x, y, z] as [number, number, number]
      };
    });
  }, [stars]);

  // Fleet positions mapped to star systems
  const fleetsBySystem = useMemo(() => {
    const fleetMap = new Map<string, any[]>();
    fleets?.forEach(fleet => {
      const systemFleets = fleetMap.get(fleet.system_id) || [];
      systemFleets.push(fleet);
      fleetMap.set(fleet.system_id, systemFleets);
    });
    return fleetMap;
  }, [fleets]);

  const handleStarClick = (star: Star) => {
    console.log(`🌟 Star clicked: ${star.name} (ID: ${star.star_id})`);

    // If already selected, zoom into solar system view
    if (star.star_id === selectedSystemId) {
      console.log(`🔍 Zooming into ${star.name} solar system`);
      setSelectedStarId(star.star_id);
      setCurrentView('solar_system');
    } else {
      // First click - select the star
      selectSystem(star.star_id);
    }
  };

  const handleBackToGalaxy = () => {
    console.log('🌌 Returning to galaxy view');
    setCurrentView('galaxy');
    setSelectedStarId(null);
  };

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-white text-xl animate-pulse">
          🌌 Initializing Galactic Visualization...
        </div>
      </div>
    );
  }

  // Render solar system view if selected
  if (currentView === 'solar_system' && selectedStarId) {
    return (
      <SolarSystemView
        starId={selectedStarId}
        onBack={handleBackToGalaxy}
      />
    );
  }

  // Render galaxy view
  return (
    <div className="w-full h-full relative">
      {/* Performance toggle and camera controls */}
      <div className="absolute top-4 right-4 z-10 space-y-2">
        <button
          onClick={() => setEnablePostProcessing(!enablePostProcessing)}
          className="block w-full px-3 py-1 bg-gray-800 bg-opacity-80 text-white text-sm rounded border border-gray-600 hover:bg-gray-700"
        >
          {enablePostProcessing ? '🎨 High Quality' : '⚡ Performance'}
        </button>
        <button
          onClick={() => resetCameraRef.current && resetCameraRef.current()}
          className="block w-full px-3 py-1 bg-blue-800 bg-opacity-80 text-white text-sm rounded border border-blue-600 hover:bg-blue-700"
        >
          🎯 Reset View
        </button>
      </div>

      {/* Instructions */}
      <div className="absolute bottom-4 left-4 z-10 bg-gray-900 bg-opacity-90 p-3 rounded-lg border border-gray-600 text-white text-sm">
        <div className="font-bold mb-1">🌟 Navigation:</div>
        <div>• Click star to select</div>
        <div>• Click selected star again to zoom into system</div>
      </div>

      {/* Hovered star info */}
      {hoveredStar && (
        <div className="absolute top-4 left-4 z-10 bg-gray-900 bg-opacity-90 p-4 rounded-lg border border-gray-600 max-w-sm">
          <h3 className="text-lg font-bold text-white mb-2">{hoveredStar.name}</h3>
          <div className="text-sm text-gray-300 space-y-1">
            <div>Type: <span className="text-blue-300">{hoveredStar.spectral_type}</span></div>
            <div>Distance: <span className="text-green-300">{hoveredStar.distance_ly?.toFixed(1)} ly</span></div>
            <div>Mass: <span className="text-yellow-300">{hoveredStar.mass_solar?.toFixed(2)} M☉</span></div>
            {hoveredStar.planet_count > 0 && (
              <div>Planets: <span className="text-purple-300">{hoveredStar.planet_count}</span></div>
            )}
          </div>
        </div>
      )}

      <Canvas
        camera={{ position: [50, 30, 50], fov: 75 }}
        style={{ background: 'transparent' }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance",
          stencil: false,
          depth: true
        }}
        dpr={[1, 2]} // Adaptive pixel ratio
      >
        {/* Enhanced lighting setup */}
        <ambientLight intensity={0.2} color="#1a1a2e" />
        <pointLight position={[0, 0, 0]} intensity={3} color="#FFD700" distance={100} />
        <pointLight position={[30, 20, 30]} intensity={1.5} color="#4A90E2" distance={80} />
        <pointLight position={[-30, -20, -30]} intensity={1.5} color="#FF6B9D" distance={80} />
        <directionalLight position={[50, 50, 50]} intensity={0.5} color="#FFFFFF" />

        {/* Environment for realistic reflections */}
        <Environment preset="night" />

        {/* Enhanced background stars */}
        <Stars
          radius={200}
          depth={100}
          count={8000}
          factor={6}
          saturation={0.1}
          fade
          speed={0.2}
        />

        {/* Volumetric nebulae - removed for cleaner view */}

        {/* Galactic center */}
        <GalacticCore />

        {/* Enhanced cosmic effects */}
        <Sparkles
          count={500}
          scale={[100, 100, 100]}
          size={3}
          speed={0.2}
          opacity={0.4}
          color="#FFD700"
        />

        {/* Render all star systems */}
        {displayStars.map((star) => (
          <StunningStarSystem
            key={star.star_id}
            star={star}
            position={star.position}
            isSelected={star.star_id === selectedSystemId}
            hasFleets={fleetsBySystem.has(star.star_id)}
            onClick={() => handleStarClick(star)}
            onHover={setHoveredStar}
          />
        ))}

        {/* Enhanced camera controls */}
        <OrbitControls
          enablePan={true} // Enable panning to navigate to star systems
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={200}
          autoRotate={false} // Disable auto-rotate to allow manual navigation
          enableDamping={true}
          dampingFactor={0.05}
          rotateSpeed={0.5}
          zoomSpeed={1.0}
          panSpeed={1.0}
          maxPolarAngle={Math.PI}
          minPolarAngle={0}
          makeDefault
        />

        <CameraController displayStars={displayStars} onResetCamera={resetCameraRef} />

        {/* Post-processing effects for stunning visuals */}
        {enablePostProcessing && (
          <Suspense fallback={null}>
            <EffectComposer>
              <Bloom
                intensity={0.8}
                luminanceThreshold={0.2}
                luminanceSmoothing={0.9}
                radius={0.8}
              />
              <ChromaticAberration offset={[0.0005, 0.0005]} />
              <Vignette eskil={false} offset={0.1} darkness={0.3} />
              <Noise opacity={0.02} />
              <ToneMapping adaptive={true} resolution={256} />
            </EffectComposer>
          </Suspense>
        )}
      </Canvas>

      {/* Performance stats */}
      <div className="absolute bottom-4 right-4 text-xs text-gray-400">
        {displayStars.length} systems • {fleets?.length || 0} fleets
      </div>
    </div>
  );
};

export default StunningGalaxy3D;
