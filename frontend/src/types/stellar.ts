// Stellar Database Types
// Type definitions for stellar and planetary data

export interface Star {
  star_id: number;
  name: string;
  catalog_name?: string;
  ra_deg: number;
  dec_deg: number;
  distance_ly: number;
  spectral_type?: string;
  mass_solar?: number;
  radius_solar?: number;
  teff_k?: number;
  luminosity_solar?: number;
  mag_v?: number;
  hz_inner_au?: number;
  hz_outer_au?: number;
  discovery_status: string;
  is_colonizable: boolean;
  planet_count: number;
}

export interface Planet {
  planet_id: number;
  name: string;
  mass_earth?: number;
  radius_earth?: number;
  sma_au?: number;
  period_days?: number;
  composition: string;
  habitability_score: number;
  in_habitable_zone: boolean;
  mineral_richness: number;
  energy_potential: number;
  discovery_year?: number;
  is_colonized: boolean;
  exploration_status: string;
}

export interface StarDetail extends Star {
  planets: Planet[];
}

export interface StellarStatistics {
  total_stars: number;
  total_planets: number;
  habitable_planets: number;
  colonized_planets: number;
  avg_star_distance: number;
  m_dwarf_count: number;
  g_dwarf_count: number;
  rocky_planets: number;
}
