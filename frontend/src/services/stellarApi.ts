// Stellar Database API Client
// Provides access to real astronomical data for the 3D galaxy map

import type { Star, Planet, StarDetail, StellarStatistics } from '../types/stellar';

// Re-export types for convenience
export type { Star, Planet, StarDetail, StellarStatistics };

class StellarApiClient {
  private baseUrl: string;

  constructor(baseUrl?: string) {
    // Auto-detect environment and use appropriate API URL
    if (baseUrl) {
      this.baseUrl = baseUrl;
    } else if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      if (hostname === 'star.omnilyzer.ai') {
        // Production environment - use fallback data (no API backend in production)
        this.baseUrl = null; // This will trigger fallback data usage
      } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
        // Local development environment - use the same hostname as the frontend
        this.baseUrl = `http://${hostname}:19081/v1`;
      } else {
        // Default fallback
        this.baseUrl = 'http://localhost:19081/v1';
      }
    } else {
      // Server-side rendering fallback
      this.baseUrl = 'http://localhost:19081/v1';
    }

    console.log('🔧 StellarAPI: Initialized with baseUrl:', this.baseUrl);
  }

  async getStarDetail(starId: number): Promise<StarDetail> {
    console.log(`🌟 StellarAPI: Fetching star detail for star_id: ${starId}`);

    // Check if we should use fallback data (production environment)
    if (!this.baseUrl) {
      console.log('🏭 StellarAPI: Production mode - using fallback star detail');
      const star = this.fallbackStars.find(s => s.star_id === starId);
      if (!star) {
        throw new Error(`Star ${starId} not found in fallback data`);
      }
      return {
        ...star,
        planets: this.getFallbackPlanets(starId)
      };
    }

    try {
      // Create timeout promise
      const timeout = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Star detail API timeout after 30 seconds')), 30000)
      );

      const cacheBuster = Date.now();
      const url = `${this.baseUrl}/stellar/stars/${starId}?_cb=${cacheBuster}`;
      console.log(`📡 StellarAPI: Fetching star detail from: ${url}`);

      const fetchPromise = fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      // Race between fetch and timeout
      const response = await Promise.race([fetchPromise, timeout]);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ StellarAPI: Star detail loaded: ${data.name} with ${data.planets?.length || 0} planets`);
      return data;
    } catch (error) {
      console.error('❌ StellarAPI: Failed to fetch star detail:', error);
      // Fallback to basic star data with empty planets
      const star = this.fallbackStars.find(s => s.star_id === starId);
      if (!star) {
        throw new Error(`Star ${starId} not found`);
      }
      return {
        ...star,
        planets: this.getFallbackPlanets(starId)
      };
    }
  }

  async getStars(maxDistance: number = 20, limit: number = 1000): Promise<{ stars: Star[]; total: number }> {
    console.log('🌟 StellarAPI: ===== STARTING STELLAR DATA FETCH =====');
    if (typeof window !== 'undefined') {
      console.log(`🌐 StellarAPI: Browser location: ${window.location.href}`);
    }

    // Check if we should use fallback data (production environment)
    if (!this.baseUrl) {
      console.log('🏭 StellarAPI: Production mode detected - using fallback data');
      console.log('⚠️ StellarAPI: USING FALLBACK DATA - NOT REAL DATABASE!');
      return {
        stars: this.fallbackStars,
        total: this.fallbackStars.length
      };
    }

    console.log(`🔗 StellarAPI: URL: ${this.baseUrl}/stellar/stars?max_distance=${maxDistance}&limit=${limit}`);

    try {
      // Create timeout promise (longer timeout)
      const timeout = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Stellar API timeout after 30 seconds')), 30000)
      );

      // Create fetch promise with cache busting
      const cacheBuster = Date.now();
      const url = `${this.baseUrl}/stellar/stars?max_distance=${maxDistance}&limit=${limit}&_cb=${cacheBuster}`;
      console.log(`📡 StellarAPI: Making fetch request to: ${url}`);

      const fetchPromise = fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      // Race between fetch and timeout
      const response = await Promise.race([fetchPromise, timeout]);

      console.log(`📡 StellarAPI: Response received! Status: ${response.status} ${response.statusText}`);
      if (response.headers && response.headers.entries) {
        console.log(`📡 StellarAPI: Response headers:`, Object.fromEntries(response.headers.entries()));
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ StellarAPI: Successfully parsed JSON response');
      console.log('📊 StellarAPI: Data structure:', {
        hasStars: !!data.stars,
        starsLength: data.stars?.length,
        hasTotal: !!data.total,
        totalValue: data.total
      });

      // Verify we got real data (should have more than 8 stars)
      if (data.stars && data.stars.length > 8) {
        console.log('🎉 StellarAPI: SUCCESS! Loaded REAL stellar database with', data.stars.length, 'stars!');
        console.log('🌟 StellarAPI: First 3 stars:', data.stars.slice(0, 3).map(s => `${s.name} (${s.distance_ly} ly)`));
      } else {
        console.warn('⚠️ StellarAPI: Only got', data.stars?.length || 0, 'stars - this might be fallback data');
      }

      return data;
    } catch (error) {
      console.error('❌ StellarAPI: FAILED to fetch real stellar data:', error);
      console.error('🔗 StellarAPI: Attempted URL:', `${this.baseUrl}/stellar/stars?max_distance=${maxDistance}&limit=${limit}`);
      console.error('🌐 StellarAPI: Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      });

      // Return fallback data for development
      console.warn('🔄 StellarAPI: Loading fallback stellar data as last resort...');
      const fallback = this.getFallbackStars();
      console.log('📦 StellarAPI: Fallback data loaded:', fallback.stars.length, 'stars');
      console.log('⚠️ StellarAPI: USING FALLBACK DATA - NOT REAL DATABASE!');
      return fallback;
    }
  }

  async getStatistics(): Promise<StellarStatistics> {
    try {
      const response = await fetch(`${this.baseUrl}/stellar/statistics`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch statistics:', error);
      // Return fallback statistics based on our fallback data
      const fallbackStars = this.getFallbackStars();
      return {
        total_stars: fallbackStars.total,
        total_planets: fallbackStars.stars.reduce((sum, star) => sum + star.planet_count, 0),
        habitable_planets: 2, // Proxima b, Tau Ceti e
        colonized_planets: 0,
        avg_star_distance: fallbackStars.stars.reduce((sum, star) => sum + star.distance_ly, 0) / fallbackStars.total,
        m_dwarf_count: fallbackStars.stars.filter(s => s.spectral_type?.startsWith('M')).length,
        g_dwarf_count: fallbackStars.stars.filter(s => s.spectral_type?.startsWith('G')).length,
        rocky_planets: fallbackStars.stars.reduce((sum, star) => sum + star.planet_count, 0)
      };
    }
  }

  // Convert astronomical coordinates to 3D Cartesian coordinates
  // This places stars in realistic 3D positions based on their actual sky coordinates and distances
  convertToCartesian(star: Star): [number, number, number] {
    const { ra_deg, dec_deg, distance_ly } = star;
    
    // Convert degrees to radians
    const ra = (ra_deg * Math.PI) / 180;
    const dec = (dec_deg * Math.PI) / 180;
    
    // Convert spherical coordinates (RA, Dec, Distance) to Cartesian (X, Y, Z)
    // Using astronomical convention: X towards vernal equinox, Y towards 90° RA, Z towards north pole
    const x = distance_ly * Math.cos(dec) * Math.cos(ra);
    const y = distance_ly * Math.cos(dec) * Math.sin(ra);
    const z = distance_ly * Math.sin(dec);
    
    // Scale down for better visualization (1 light-year = 1 unit in 3D space)
    return [x, y, z];
  }

  // Get star color based on spectral type
  getStarColor(spectralType?: string): string {
    if (!spectralType) return '#FFFFFF';
    
    const type = spectralType.charAt(0).toUpperCase();
    switch (type) {
      case 'O': return '#9BB0FF'; // Blue
      case 'B': return '#AABFFF'; // Blue-white
      case 'A': return '#CAD7FF'; // White
      case 'F': return '#F8F7FF'; // Yellow-white
      case 'G': return '#FFF4EA'; // Yellow (like our Sun)
      case 'K': return '#FFD2A1'; // Orange
      case 'M': return '#FFAD51'; // Red
      case 'L': return '#FF6B35'; // Brown dwarf
      case 'D': return '#FFFFFF'; // White dwarf
      default: return '#FFFFFF';
    }
  }

  // Get star size multiplier based on spectral type and radius
  getStarSize(star: Star): number {
    const baseSize = 0.8;
    
    // Use actual radius if available
    if (star.radius_solar && star.radius_solar > 0) {
      return Math.max(0.3, Math.min(3.0, baseSize * Math.sqrt(star.radius_solar)));
    }
    
    // Fallback to spectral type estimation
    if (!star.spectral_type) return baseSize;
    
    const type = star.spectral_type.charAt(0).toUpperCase();
    switch (type) {
      case 'O': return baseSize * 2.5; // Massive blue giants
      case 'B': return baseSize * 2.0; // Large blue stars
      case 'A': return baseSize * 1.5; // Medium white stars
      case 'F': return baseSize * 1.2; // Slightly larger than Sun
      case 'G': return baseSize * 1.0; // Sun-like
      case 'K': return baseSize * 0.8; // Smaller orange stars
      case 'M': return baseSize * 0.6; // Small red dwarfs
      case 'L': return baseSize * 0.4; // Brown dwarfs
      case 'D': return baseSize * 0.2; // White dwarfs
      default: return baseSize;
    }
  }

  // Fallback data for development when API is not available
  private getFallbackStars(): { stars: Star[]; total: number } {
    const fallbackStars: Star[] = [
      {
        star_id: 1,
        name: 'Sol',
        ra_deg: 0,
        dec_deg: 0,
        distance_ly: 0,
        spectral_type: 'G2V',
        mass_solar: 1.0,
        radius_solar: 1.0,
        teff_k: 5778,
        luminosity_solar: 1.0,
        mag_v: 4.83,
        discovery_status: 'known',
        is_colonizable: true,
        planet_count: 8
      },
      {
        star_id: 2,
        name: 'Proxima Centauri',
        ra_deg: 217.43,
        dec_deg: -62.68,
        distance_ly: 4.24,
        spectral_type: 'M5.5Ve',
        mass_solar: 0.12,
        radius_solar: 0.15,
        teff_k: 3042,
        luminosity_solar: 0.0017,
        mag_v: 11.13,
        discovery_status: 'known',
        is_colonizable: true,
        planet_count: 3
      },
      {
        star_id: 3,
        name: 'Alpha Centauri A',
        ra_deg: 219.90,
        dec_deg: -60.83,
        distance_ly: 4.37,
        spectral_type: 'G2V',
        mass_solar: 1.1,
        radius_solar: 1.22,
        teff_k: 5790,
        luminosity_solar: 1.52,
        mag_v: 0.01,
        discovery_status: 'known',
        is_colonizable: true,
        planet_count: 0
      },
      {
        star_id: 4,
        name: 'Barnard\'s Star',
        ra_deg: 269.45,
        dec_deg: 4.69,
        distance_ly: 5.96,
        spectral_type: 'M4.0V',
        mass_solar: 0.14,
        radius_solar: 0.20,
        teff_k: 3134,
        luminosity_solar: 0.0035,
        mag_v: 9.53,
        discovery_status: 'known',
        is_colonizable: true,
        planet_count: 1
      },
      {
        star_id: 5,
        name: 'Wolf 359',
        ra_deg: 164.12,
        dec_deg: 7.01,
        distance_ly: 7.86,
        spectral_type: 'M6.0V',
        mass_solar: 0.09,
        radius_solar: 0.16,
        teff_k: 2800,
        luminosity_solar: 0.0014,
        mag_v: 13.44,
        discovery_status: 'known',
        is_colonizable: true,
        planet_count: 1
      },
      {
        star_id: 6,
        name: 'Sirius A',
        ra_deg: 101.29,
        dec_deg: -16.72,
        distance_ly: 8.66,
        spectral_type: 'A1V',
        mass_solar: 2.06,
        radius_solar: 1.71,
        teff_k: 9940,
        luminosity_solar: 25.4,
        mag_v: -1.46,
        discovery_status: 'known',
        is_colonizable: true,
        planet_count: 0
      },
      {
        star_id: 7,
        name: 'Epsilon Eridani',
        ra_deg: 53.23,
        dec_deg: -9.46,
        distance_ly: 10.52,
        spectral_type: 'K2V',
        mass_solar: 0.82,
        radius_solar: 0.74,
        teff_k: 5084,
        luminosity_solar: 0.34,
        mag_v: 3.73,
        discovery_status: 'known',
        is_colonizable: true,
        planet_count: 1
      },
      {
        star_id: 8,
        name: 'Tau Ceti',
        ra_deg: 26.02,
        dec_deg: -15.94,
        distance_ly: 11.91,
        spectral_type: 'G8.5V',
        mass_solar: 0.78,
        radius_solar: 0.79,
        teff_k: 5344,
        luminosity_solar: 0.52,
        mag_v: 3.49,
        discovery_status: 'known',
        is_colonizable: true,
        planet_count: 4
      }
    ];

    console.log('🔄 Using fallback stellar data:', fallbackStars.length, 'stars');
    return { stars: fallbackStars, total: fallbackStars.length };
  }

  private getFallbackPlanets(starId: number): Planet[] {
    // Generate some realistic fallback planets based on star ID
    const planetData: { [key: number]: Planet[] } = {
      1: [ // Sol
        {
          planet_id: 1,
          name: 'Mercury',
          mass_earth: 0.055,
          radius_earth: 0.383,
          sma_au: 0.387,
          period_days: 88,
          composition: 'rocky',
          habitability_score: 0.0,
          in_habitable_zone: false,
          mineral_richness: 0.8,
          energy_potential: 0.9,
          is_colonized: false,
          exploration_status: 'surveyed'
        },
        {
          planet_id: 2,
          name: 'Venus',
          mass_earth: 0.815,
          radius_earth: 0.949,
          sma_au: 0.723,
          period_days: 225,
          composition: 'rocky',
          habitability_score: 0.0,
          in_habitable_zone: false,
          mineral_richness: 0.7,
          energy_potential: 0.3,
          is_colonized: false,
          exploration_status: 'surveyed'
        },
        {
          planet_id: 3,
          name: 'Earth',
          mass_earth: 1.0,
          radius_earth: 1.0,
          sma_au: 1.0,
          period_days: 365.25,
          composition: 'rocky',
          habitability_score: 1.0,
          in_habitable_zone: true,
          mineral_richness: 0.6,
          energy_potential: 0.8,
          is_colonized: true,
          exploration_status: 'colonized'
        },
        {
          planet_id: 4,
          name: 'Mars',
          mass_earth: 0.107,
          radius_earth: 0.532,
          sma_au: 1.524,
          period_days: 687,
          composition: 'rocky',
          habitability_score: 0.3,
          in_habitable_zone: true,
          mineral_richness: 0.9,
          energy_potential: 0.4,
          is_colonized: false,
          exploration_status: 'surveyed'
        }
      ],
      2: [ // Proxima Centauri
        {
          planet_id: 5,
          name: 'Proxima Centauri b',
          mass_earth: 1.17,
          radius_earth: 1.1,
          sma_au: 0.0485,
          period_days: 11.186,
          composition: 'rocky',
          habitability_score: 0.7,
          in_habitable_zone: true,
          mineral_richness: 0.8,
          energy_potential: 0.3,
          is_colonized: false,
          exploration_status: 'unexplored'
        }
      ]
    };

    return planetData[starId] || [];
  }
}

export const stellarApi = new StellarApiClient();
