import { useEffect, useState } from 'react';
import { useGameStore } from './store/gameStore';
import GameHUD from './components/GameHUD';
import StunningGalaxy3D from './components/StunningGalaxy3D';
import FleetList from './components/FleetList';
import OrderForm from './components/OrderForm';
import LoadingScreen from './components/LoadingScreen';
import ErrorDisplay from './components/ErrorDisplay';
import Tutorial from './components/Tutorial';
import ResourceDisplay from './components/ResourceDisplay';
import ColonyManagement from './components/ColonyManagement';
import TechnologyTree from './components/TechnologyTree';
import { Market } from './components/Market';
import WebGLFallback from './components/WebGLFallback';
import ErrorBoundary from './components/ErrorBoundary';
import SolarSystemDemo from './components/SolarSystemDemo';

function App() {
  console.log('🎯 App: Component starting to render');
  console.log('🕐 App: Render timestamp:', new Date().toISOString());

  const {
    initialize,
    isLoading,
    error,
    showOrderForm,
    showTutorialModal
  } = useGameStore();

  const [activeModal, setActiveModal] = useState<'colonies' | 'technology' | 'market' | 'solar-demo' | null>(null);
  const [webglError, setWebglError] = useState(false);
  const [webglRetryCount, setWebglRetryCount] = useState(0);

  useEffect(() => {
    initialize();

    // Check WebGL support
    const checkWebGL = () => {
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) {
          console.error('❌ WebGL not supported');
          setWebglError(true);
          return false;
        }
        console.log('✅ WebGL supported');
        return true;
      } catch (e) {
        console.error('❌ WebGL error:', e);
        setWebglError(true);
        return false;
      }
    };

    // Listen for WebGL context lost errors
    const handleWebGLError = (event: Event) => {
      console.error('❌ WebGL context error:', event);
      setWebglError(true);
    };

    window.addEventListener('webglcontextlost', handleWebGLError);
    window.addEventListener('webglcontextcreationerror', handleWebGLError);

    // Check WebGL after a short delay to let the page load
    setTimeout(checkWebGL, 1000);

    // Show tutorial for first-time players
    const hasSeenTutorial = localStorage.getItem('galactic-genesis-tutorial-seen');
    if (!hasSeenTutorial) {
      // Delay tutorial to let the game load first
      setTimeout(() => {
        useGameStore.getState().showTutorial();
      }, 2000);
    }

    return () => {
      window.removeEventListener('webglcontextlost', handleWebGLError);
      window.removeEventListener('webglcontextcreationerror', handleWebGLError);
    };

    // Add global helper functions for development
    (window as any).resetTutorial = () => {
      localStorage.removeItem('galactic-genesis-tutorial-seen');
      console.log('Tutorial reset! Refresh the page to see it again.');
    };

    (window as any).showTutorial = () => {
      useGameStore.getState().showTutorial();
    };

    // WebSocket development utilities
    (window as any).disableWebSocket = () => {
      const { wsService } = require('./services/websocket');
      wsService.disable();
      console.log('🔇 WebSocket disabled');
    };

    (window as any).reconnectWebSocket = () => {
      const { wsService } = require('./services/websocket');
      wsService.connect();
      console.log('🔌 WebSocket reconnection attempted');
    };

    // Test API function
    (window as any).testMoveOrder = async () => {
      try {
        const response = await fetch('http://localhost:19080/v1/orders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Idempotency-Key': 'test-browser-' + Date.now()
          },
          body: JSON.stringify({
            kind: 'move',
            payload: {
              fleetId: '522bd8de-abd7-45ad-bfd1-35ee655d5592',
              toSystemId: 'sys-1'
            }
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('API Error:', response.status, errorText);
          return;
        }

        const result = await response.json();
        console.log('Move order success:', result);
      } catch (error) {
        console.error('Request failed:', error);
      }
    };
  }, [initialize]);

  // Handle ESC key to close modals
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setActiveModal(null);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  if (isLoading && !useGameStore.getState().fleets.length) {
    return <LoadingScreen />;
  }

  // Show WebGL fallback if there's an error
  if (webglError) {
    return (
      <WebGLFallback
        onRetry={() => {
          setWebglError(false);
          setWebglRetryCount(prev => prev + 1);
          // Force a page reload after 3 retries
          if (webglRetryCount >= 2) {
            window.location.reload();
          }
        }}
      />
    );
  }

  return (
    <div className="h-screen w-screen bg-gray-900 starfield-bg overflow-hidden">
      {/* Game HUD - Top bar */}
      <GameHUD />

      {/* Resource Display */}
      <div className="px-4 pt-16">
        <ResourceDisplay empireId="emp-1" />
      </div>

      {/* Main game area */}
      <div className="flex h-full pt-4">
        {/* Left sidebar - Fleet management */}
        <div className="w-80 glass-panel m-4 p-4 overflow-y-auto">
          <FleetList />
        </div>

        {/* Center - Galaxy map */}
        <div className="flex-1 relative">
          <ErrorBoundary
            fallback={
              <WebGLFallback
                onRetry={() => {
                  setWebglError(false);
                  setWebglRetryCount(prev => prev + 1);
                  window.location.reload();
                }}
              />
            }
            onError={(error) => {
              console.error('❌ 3D Rendering Error:', error);
              if (error.message.includes('WebGL') || error.message.includes('context')) {
                setWebglError(true);
              }
            }}
          >
            <StunningGalaxy3D />
          </ErrorBoundary>
        </div>

        {/* Right sidebar - Action buttons */}
        <div className="w-20 flex flex-col gap-4 m-4">
          <button
            onClick={() => setActiveModal('colonies')}
            className="glass-panel p-4 text-center hover:bg-gray-700 hover:border-cyan-400 transition-all duration-200 group transform hover:scale-105"
            title="Manage Colonies"
          >
            <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">🏛️</div>
            <div className="text-xs text-gray-400 group-hover:text-cyan-400 transition-colors">Colonies</div>
          </button>

          <button
            onClick={() => setActiveModal('technology')}
            className="glass-panel p-4 text-center hover:bg-gray-700 hover:border-cyan-400 transition-all duration-200 group transform hover:scale-105"
            title="Research Technology"
          >
            <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">🔬</div>
            <div className="text-xs text-gray-400 group-hover:text-cyan-400 transition-colors">Tech</div>
          </button>

          <button
            onClick={() => setActiveModal('market')}
            className="glass-panel p-4 text-center hover:bg-gray-700 hover:border-cyan-400 transition-all duration-200 group transform hover:scale-105"
            title="Galactic Market"
          >
            <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">💰</div>
            <div className="text-xs text-gray-400 group-hover:text-cyan-400 transition-colors">Market</div>
          </button>

          <button
            onClick={() => setActiveModal('solar-demo')}
            className="glass-panel p-4 text-center hover:bg-gray-700 hover:border-cyan-400 transition-all duration-200 group transform hover:scale-105"
            title="Solar System Demo"
          >
            <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">🌌</div>
            <div className="text-xs text-gray-400 group-hover:text-cyan-400 transition-colors">Solar</div>
          </button>
        </div>
      </div>

      {/* Management Modals */}
      {activeModal === 'colonies' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-gray-900 rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden transform animate-slideUp">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">🏛️ Colony Management</h2>
              <button
                onClick={() => setActiveModal(null)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
              <ColonyManagement empireId="emp-1" />
            </div>
          </div>
        </div>
      )}

      {activeModal === 'technology' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-gray-900 rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden transform animate-slideUp">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">🔬 Technology Tree</h2>
              <button
                onClick={() => setActiveModal(null)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
              <TechnologyTree empireId="emp-1" />
            </div>
          </div>
        </div>
      )}

      {activeModal === 'market' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-gray-900 rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden transform animate-slideUp">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">💰 Galactic Market</h2>
              <button
                onClick={() => setActiveModal(null)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
              <Market empireId="emp-1" />
            </div>
          </div>
        </div>
      )}

      {activeModal === 'solar-demo' && (
        <div className="fixed inset-0 bg-black z-50">
          <SolarSystemDemo onBack={() => setActiveModal(null)} />
        </div>
      )}

      {/* Order form modal */}
      {showOrderForm && <OrderForm />}

      {/* Tutorial modal */}
      {showTutorialModal && <Tutorial />}

      {/* Error display */}
      {error && <ErrorDisplay error={error} />}
    </div>
  );
}

export default App;
